<template>
  <div
    class="track-line"
    :style="TrackHeightMap.get(lineType)"
    :data-index="lineIndex"
    :data-type="lineType"
  >
    <template v-for="(item, index) of lineData" :key="item.id">
      <TrackItem
        :line-index="lineIndex"
        :item-index="index"
        :track-item="item as any"
      />
    </template>
  </div>
</template>

<script setup lang="ts">
  import TrackItem from './track-item.vue';

  defineProps({
    lineType: {
      type: String,
      default: '',
    },
    lineIndex: {
      type: Number,
      default: 0,
    },
    lineData: {
      type: Array,
      default() {
        return [];
      },
    },
  });

  const TrackHeightMap = new Map([
    [
      'video',
      {
        height: '40px',
      },
    ],
    [
      'text',
      {
        height: '24px',
      },
    ],
    [
      'image',
      {
        height: '32px',
      },
    ],
  ]);
</script>

<style scoped>
  .track-line {
    margin-bottom: 12px;
    position: relative;
    margin-left: 5px;
    display: flex;
  }

  .active-line {
    border: 4px solid green;
    border-radius: 2px;
  }
</style>
