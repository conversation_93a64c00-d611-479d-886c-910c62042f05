import { MediaType } from '@/views/promotion/hooks/authorization';
import {
  AccountStatus,
  accountTableStatus,
  batchReplacePlatformAccounts,
  otherTableStatus,
} from '@/views/task/account-task/constants';
import { TableColumnData } from '@arco-design/web-vue';

/**
 * 获取基础账户信息列配置
 */
function getAccountColumns(
  mediaType: MediaType = null as unknown as MediaType
): TableColumnData[] {
  const columns: TableColumnData[] = [
    {
      title: '操作对象',
      dataIndex: 'operationTargetName',
      ellipsis: true,
      tooltip: true,
      minWidth: 150,
    },
    {
      title: '操作内容',
      dataIndex: 'operationContent',
      slotName: 'operationContent',
      width: 350,
      ellipsis: true,
      minWidth: 150,
    },
    {
      title: '创建时间',
      dataIndex: 'createAt',
      ellipsis: true,
      tooltip: true,
      minWidth: 150,
    },
    {
      title: '执行结果',
      dataIndex: 'status',
      ellipsis: true,
      tooltip: true,
      minWidth: 150,
      render: ({ record }) => {
        return accountTableStatus.filter(
          (statusItem) => statusItem.value === record.status
        )[0].label;
      },
    },
    ...(batchReplacePlatformAccounts.includes(mediaType)
      ? [
          {
            title: '数量',
            dataIndex: 'executeDetail',
            render: ({ record }) => {
              return record.status === AccountStatus.PartSuccess
                ? record.executeDetail
                : '-';
            },
          },
        ]
      : []),
    {
      title: '失败原因',
      dataIndex: 'failReason',
      ellipsis: true,
      tooltip: true,
      minWidth: 150,
    },
    {
      title: '操作',
      dataIndex: 'operations',
      slotName: 'operations',
      fixed: 'right',
      width: 100,
    },
  ];

  return columns;
}

/**
 * 获取失败原因分布列配置
 */
function getFailReasonColumns(): TableColumnData[] {
  return [
    {
      title: '维度',
      dataIndex: 'dimension',
    },
    { title: '失败原因', dataIndex: 'failReasonDetails.failReason' },
    { title: '次数', dataIndex: 'failReasonDetails.count' },
  ];
}

/**
 * 获取通用的时间和状态列配置
 */
function getCommonColumns(): TableColumnData[] {
  return [
    {
      title: '创建时间',
      dataIndex: 'createAt',
      ellipsis: true,
      tooltip: true,
      minWidth: 150,
    },
    {
      title: '更新时间',
      dataIndex: 'updateAt',
      ellipsis: true,
      tooltip: true,
      minWidth: 150,
    },
    {
      title: '执行结果',
      dataIndex: 'status',
      ellipsis: true,
      tooltip: true,
      minWidth: 150,
      render: ({ record }) => {
        return otherTableStatus.filter(
          (statusItem) => statusItem.value === record.status
        )[0]?.label;
      },
    },
    {
      title: '失败原因',
      dataIndex: 'failReason',
      ellipsis: true,
      tooltip: true,
      minWidth: 150,
    },
  ];
}

/**
 * 获取头条平台的列配置
 */
function getTouTiaoColumns(): TableColumnData[][] {
  const commonColumns = getCommonColumns();

  return [
    // Tab1: 账户信息 - 使用基础配置
    [], // 占位，会在主函数中替换
    // Tab2: 项目信息
    [
      { title: '账户ID', dataIndex: 'advertiserId' },
      {
        title: '账户名称',
        dataIndex: 'advertiserName',
        ellipsis: true,
        tooltip: true,
      },
      { title: '项目ID', dataIndex: 'mediaId' },
      { title: '项目名称', dataIndex: 'mediaName' },
      {
        title: '原监测链接',
        dataIndex: 'originConfig',
        ellipsis: true,
        tooltip: true,
      },
      {
        title: '现监测链接',
        dataIndex: 'modifyConfig',
        ellipsis: true,
        tooltip: true,
      },
      ...commonColumns,
    ],
    // Tab3: 广告信息
    [
      { title: '账户ID', dataIndex: 'advertiserId' },
      {
        title: '账户名称',
        dataIndex: 'advertiserName',
        ellipsis: true,
        tooltip: true,
      },
      { title: '广告ID', dataIndex: 'mediaId' },
      { title: '广告名称', dataIndex: 'mediaName' },
      {
        title: '原快应用链接',
        dataIndex: 'originConfig',
        ellipsis: true,
        tooltip: true,
      },
      {
        title: '现快应用链接',
        dataIndex: 'modifyConfig',
        ellipsis: true,
        tooltip: true,
      },
      ...commonColumns,
    ],
    // Tab4: 落地页信息
    [
      { title: '账户ID', dataIndex: 'advertiserId' },
      {
        title: '账户名称',
        dataIndex: 'advertiserName',
        ellipsis: true,
        tooltip: true,
      },
      { title: '落地页ID', dataIndex: 'mediaId' },
      { title: '落地页名称', dataIndex: 'mediaName' },
      {
        title: '原快应用链接',
        dataIndex: 'originConfig',
        ellipsis: true,
        tooltip: true,
      },
      {
        title: '现快应用链接',
        dataIndex: 'modifyConfig',
        ellipsis: true,
        tooltip: true,
      },
      ...commonColumns,
    ],
    // Tab5: 失败原因分布
    [
      {
        title: '维度',
        dataIndex: 'dimension',
      },
      { title: '失败原因', dataIndex: 'failReasonDetails.failReason' },
      { title: '次数', dataIndex: 'failReasonDetails.count' },
    ],
  ];
}

/**
 * 获取快手平台的列配置
 */
function getKuaiShouColumns(): TableColumnData[][] {
  const commonColumns = getCommonColumns();

  return [
    // Tab1: 账户信息 - 使用基础配置
    [], // 占位，会在主函数中替换
    // Tab2: 广告信息
    [
      { title: '账户ID', dataIndex: 'advertiserId' },
      {
        title: '账户名称',
        dataIndex: 'advertiserName',
        ellipsis: true,
        tooltip: true,
      },
      { title: '广告组ID', dataIndex: 'mediaId' },
      { title: '广告组名称', dataIndex: 'mediaName' },
      {
        title: '原应用直达链接',
        dataIndex: 'originConfig',
        ellipsis: true,
        tooltip: true,
      },
      {
        title: '现应用直达链接',
        dataIndex: 'modifyConfig',
        ellipsis: true,
        tooltip: true,
      },
      ...commonColumns,
    ],
    // Tab3: 创意信息
    [
      { title: '账户ID', dataIndex: 'advertiserId' },
      {
        title: '账户名称',
        dataIndex: 'advertiserName',
        ellipsis: true,
        tooltip: true,
      },
      { title: '创意ID', dataIndex: 'mediaId' },
      { title: '创意名称', dataIndex: 'mediaName' },
      {
        title: '原监测链接',
        dataIndex: 'originConfig',
        ellipsis: true,
        tooltip: true,
      },
      {
        title: '现监测链接',
        dataIndex: 'modifyConfig',
        ellipsis: true,
        tooltip: true,
      },
      ...commonColumns,
    ],
    // Tab4: 失败原因分布
    [
      {
        title: '维度',
        dataIndex: 'dimension',
      },
      { title: '失败原因', dataIndex: 'failReasonDetails.failReason' },
      { title: '次数', dataIndex: 'failReasonDetails.count' },
    ],
  ];
}

/**
 * 根据平台类型获取列配置
 * @param mediaType 媒体平台类型
 * @returns 列配置数组
 */
export function getColumnsConfig(mediaType: MediaType): TableColumnData[][] {
  switch (mediaType) {
    case MediaType.TOU_TIAO: {
      const accountColumns = getAccountColumns(mediaType);
      const failReasonColumns = getFailReasonColumns();
      const columns = getTouTiaoColumns();

      return [accountColumns, ...columns, failReasonColumns];
    }

    case MediaType.KUAI_SHOU: {
      const accountColumns = getAccountColumns(mediaType);
      const failReasonColumns = getFailReasonColumns();
      const columns = getKuaiShouColumns();

      return [accountColumns, ...columns, failReasonColumns];
    }

    default:
      return [getAccountColumns()];
  }
}
