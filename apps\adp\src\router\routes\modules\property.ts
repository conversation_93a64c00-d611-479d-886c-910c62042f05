import IconJUL from '@/components/icons/juliang.vue';
import IconCILI from '@/components/icons/cili.vue';
import IconGDT from '@/components/icons/guangdiantong.vue';
import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const PROPERTY_MANAGE: AppRouteRecordRaw = {
  path: '/property',
  name: 'property',
  component: DEFAULT_LAYOUT,
  meta: {
    title: '资产管理',
    requiresAuth: true,
    // icon: 'icon-storage',
    order: 0,
  },
  children: [
    {
      path: '/property/tt',
      name: 'propertyTt',
      meta: {
        title: '今日头条',
        requiresAuth: true,
        roles: ['admin'],
        icon: IconJUL as unknown as string,
        subName: 'propertyTtPackage',
      },
      children: [
        {
          path: '/property/tt/package',
          name: 'propertyTtPackage',
          component: () => import('@/views/property-manage/toutiao/index.vue'),
          meta: {
            title: '定向包',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/property/tt/LandingPage',
          name: 'propertyTtLandingPage',
          component: () =>
            import('@/views/property-manage/toutiao/landing-page/index.vue'),
          meta: {
            title: '落地页',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
      ],
    },

    {
      path: '/property/ks',
      name: 'propertyKs',
      meta: {
        title: '快手',
        requiresAuth: true,
        roles: ['admin'],
        icon: IconCILI as unknown as string,
      },
      children: [
        {
          path: '/property/ks/package',
          name: 'propertyKsPackage',
          component: () => import('@/views/property-manage/kuaishou/index.vue'),
          meta: {
            title: '定向包',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/property/ks/promote',
          name: 'propertyKsPromote',
          component: () =>
            import('@/views/property-manage/kuaishou/promote/index.vue'),
          meta: {
            title: '推广内容',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
      ],
    },

    {
      path: '/property/gdt',
      name: 'propertyGdt',
      meta: {
        title: '广点通3.0',
        requiresAuth: true,
        roles: ['admin'],
        icon: IconGDT as unknown as string,
        subName: 'propertyGdtPackage',
      },
      children: [
        {
          path: '/property/gdt/package',
          name: 'propertyGdtPackage',
          component: () =>
            import('@/views/property-manage/guangdiantong/index.vue'),
          meta: {
            title: '定向模版',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
      ],
    },
  ],
};

export default PROPERTY_MANAGE;
