<template>
  <section>
    <a-form :model="filterForm" :style="{ width: '420px' }" v-if="isShowFilter">
      <a-form-item field="section" label="执行结果">
        <a-select
          v-model="filterForm.section"
          multiple
          placeholder="请选择执行结果"
          allow-clear
          :options="options"
          @popup-visible-change="handleSeleteChange"
        >
        </a-select>
      </a-form-item>
    </a-form>
    <a-tabs v-model:active-key="currentKey" @change="handleTabChange">
      <a-tab-pane
        v-for="(tab, index) in tabsConfig"
        :key="tab.key"
        :title="tab.title"
      >
        <a-table
          :columns="columnsList[index]"
          :data="tableDataList[tab.key] || []"
          :bordered="{ headerCell: true }"
          :loading="loading"
          column-resizable
          :pagination="isNeedPagination ? paginationParams : false"
          @page-change="handlePageChange"
          @page-size-change="handlePageSizeChange"
          :scrollbar="true"
          :scroll="{
            x: '100%',
            y: '100%',
          }"
        >
          <template #operationContent="{ record: row }">
            <a-tooltip position="tl">
              <template #content>
                <div style="max-height: 300px; overflow-y: auto">
                  <span v-for="item in row.showList" :key="item.id">
                    <span style="margin-right: 5px; word-break: break-all">
                      {{ item.oldPropertyName }}：{{ item.oldTargetName }}
                    </span>
                    <span> >> </span>
                    <span style="word-break: break-all">
                      {{ item.newTargetName }}
                    </span>
                    <br />
                  </span>
                </div>
              </template>
              <span class="operation-content">
                <span v-for="item in row.showList" :key="item.id">
                  <span style="margin-right: 5px">
                    {{ item.oldPropertyName }}：{{ item.oldTargetName }}
                  </span>
                  <span> >> </span>
                  <span>
                    {{ item.newTargetName }}
                  </span>
                  <br />
                </span>
              </span>
            </a-tooltip>
          </template>
          <template #operations="{ record: row }">
            <a-button
              type="text"
              @click="handleCancel(row)"
              :disabled="disabledCancel(row)"
            >
              终止
            </a-button>
          </template>
        </a-table>
      </a-tab-pane>
    </a-tabs>
  </section>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import {
    COMPLETED_STATUSES,
    accountTableStatus,
    batchReplacePlatformAccounts,
    otherTableStatus,
  } from '@/views/task/account-task/constants';
  import { getTabsConfig } from './config/tabs-config';
  import { getColumnsConfig } from './config/columns-config';
  import { useTaskDetail } from './composables/useTaskDetail';

  const { record, currentPage } = defineProps<{
    record: any;
    currentPage: any;
  }>();

  defineOptions({
    inheritAttrs: false,
  });

  // 判断当前 tab 是否为账户 tab
  const isAccountTab = computed(() => currentKey.value === 'ACCOUNT');

  // 是否需要分页:账户 失败原因 不需要分页
  const isNeedPagination = computed(() => {
    return !['ACCOUNT', 'FAIL_REASON'].includes(currentKey.value as string);
  });

  const options = computed(() => {
    return isAccountTab.value ? accountTableStatus : otherTableStatus;
  });

  // 判断当前 mediaType && 当前 tab 不是失败原因tab
  const isShowFilter = computed(
    () =>
      batchReplacePlatformAccounts.includes(currentPage.mediaType) &&
      currentKey.value !== 'FAIL_REASON'
  );

  // 使用 composable 管理数据逻辑
  const {
    currentKey,
    filterForm,
    tableDataList,
    loading,
    paginationParams,
    fetchDetail,
    handleCancel,
    handlePageChange,
    handlePageSizeChange,
    handleTabChange,
    initData,
  } = useTaskDetail(record, currentPage);

  const handleSeleteChange = (visible: boolean) => {
    if (!visible) {
      fetchDetail();
    }
  };

  // Tab 配置：根据平台动态生成
  const tabsConfig = computed(() =>
    getTabsConfig(currentPage.mediaType, record)
  );

  // 列配置：根据平台和任务状态动态生成
  const columnsList = computed(() => getColumnsConfig(currentPage.mediaType));

  function disabledCancel(row: any) {
    return COMPLETED_STATUSES.has(row.status);
  }

  // 初始化数据
  onMounted(() => {
    initData();
  });
</script>

<style scoped lang="less">
  .operation-content {
    display: inline;
  }

  :deep(.arco-tabs-pane) {
    height: calc(100vh - 320px);
  }
</style>
