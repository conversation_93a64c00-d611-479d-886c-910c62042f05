<template>
  <a-layout class="layout" :class="{ mobile: appStore.hideMenu }">
    <div v-if="navbar" class="layout-navbar">
      <NavBar />
    </div>
    <a-layout>
      <a-layout-sider
        v-if="renderMenu"
        v-show="!hideMenu"
        class="layout-sider"
        breakpoint="xl"
        :collapsed="collapsed"
        :collapsible="true"
        :width="menuWidth"
        :hide-trigger="true"
        @collapse="setCollapsed"
      >
        <div class="menu-wrapper">
          <Menu @sub-menu-load="subMenuLoad" />
        </div>
      </a-layout-sider>
      <a-drawer
        v-if="hideMenu"
        :visible="drawerVisible"
        placement="left"
        :footer="false"
        mask-closable
        :closable="false"
        @cancel="drawerCancel"
      >
        <Menu />
      </a-drawer>
      <a-layout class="layout-content">
        <TabBar v-if="appStore.tabBar" />
        <div id="portalNav" class="portal-nav" :style="portalNavStyle"></div>
        <a-layout-content
          class="layout-core"
          id="layoutCore"
          :style="coreLayoutStyle"
        >
          <template v-if="appStore.notAppList">
            <div class="center-content">
              <a-empty description="用户未查询到应用，请联系管理员" />
            </div>
          </template>
          <template v-else>
            <template v-if="!appStore.appInfo.loading">
              <PageLayout />
            </template>
            <div v-else class="center-content">
              <a-spin dot />
            </div>
          </template>
        </a-layout-content>
        <div id="portalFooter"></div>
      </a-layout>
    </a-layout>
  </a-layout>
</template>

<script lang="ts" setup>
  import { ref, computed, watch, provide, onMounted, nextTick } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { useAppStore, useUserStore } from '@/store';
  import NavBar from '@/components/navbar/index.vue';
  import Menu from '@/components/menu/index.vue';
  import TabBar from '@/components/tab-bar/index.vue';
  import usePermission from '@/hooks/permission';
  import useResponsive from '@/hooks/responsive';
  import PageLayout from './page-layout.vue';

  const isInit = ref(false);
  const appStore = useAppStore();
  const userStore = useUserStore();
  const router = useRouter();
  const route = useRoute();
  const permission = usePermission();
  useResponsive(true);
  const navbarHeight = `60px`;
  const navbar = computed(() => appStore.navbar);
  const renderMenu = computed(() => appStore.menu && !appStore.topMenu);
  const hideMenu = computed(
    () => appStore.hideMenu || subMenuList.value?.length <= 0
  );

  const menuWidth = computed(() => {
    return appStore.menuCollapse ? 48 : appStore.menuWidth;
  });
  const coreLayoutStyle = computed(() => appStore.coreLayoutStyle);
  const portalNavStyle = computed(() => appStore.portalNavStyle);
  const collapsed = computed(() => {
    return appStore.menuCollapse;
  });

  const subMenuList = ref([]);

  function subMenuLoad(list) {
    subMenuList.value = list;
  }

  const setCollapsed = (val: boolean) => {
    if (!isInit.value) return; // for page initialization menu state problem
    appStore.updateSettings({ menuCollapse: val });
  };
  watch(
    () => userStore.role,
    (roleValue) => {
      if (roleValue && !permission.accessRouter(route))
        router.push({ name: 'notFound' });
    }
  );
  const drawerVisible = ref(false);
  const drawerCancel = () => {
    drawerVisible.value = false;
  };
  provide('toggleDrawerMenu', () => {
    drawerVisible.value = !drawerVisible.value;
  });
  onMounted(() => {
    isInit.value = true;
  });
  appStore.initAppInfo();
</script>

<style scoped lang="less">
  @nav-size-height: 60px;
  @layout-max-width: 1100px;

  .layout {
    width: 100%;
    height: 100%;
  }

  .layout-navbar {
    z-index: 101;
    width: 100%;
    height: @nav-size-height;
  }

  .layout-sider {
    height: calc(100vh - 60px);
    transition: all 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);

    &::after {
      position: absolute;
      top: 0;
      right: -1px;
      display: block;
      width: 1px;
      height: 100%;
      background-color: var(--color-border);
      content: '';
    }

    > :deep(.arco-layout-sider-children) {
      overflow-y: hidden;
    }
  }

  .menu-wrapper {
    height: 100%;
    overflow: auto;
    overflow-x: hidden;

    :deep(.arco-menu) {
      ::-webkit-scrollbar {
        width: 12px;
        height: 4px;
      }

      ::-webkit-scrollbar-thumb {
        border: 4px solid transparent;
        background-clip: padding-box;
        border-radius: 7px;
        background-color: var(--color-text-4);
      }

      ::-webkit-scrollbar-thumb:hover {
        background-color: var(--color-text-3);
      }
    }
  }

  .layout-content {
    height: calc(100vh - 60px);
    transition: padding 0.2s cubic-bezier(0.34, 0.69, 0.1, 1);

    .center-content {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .layout-core {
      background-color: #f5f5f5;
      padding: 16px 20px;
      height: 100%;
      overflow: hidden auto;
    }

    .portal-nav {
      height: 48px;
      background-color: #fff;
      border-bottom: 1px solid rgb(229 230 235);
    }
  }
</style>
