import { nextTick } from 'vue';
import { useTrees } from '@/hooks/material-tree';
import { PushPlatformOptions } from '@/constants/material';
import DirSelect from '@/views/sc-home/components/dir-select.vue';
import TemplateList from './components/template-list.vue';
import { CONSTANT } from './constant';

const { trees } = useTrees();

export function getBaseFormValues() {
  return {
    startType: 1, // 1: 指定文件夹, 2: 三方平台
    source: 1,
    thirdPlatform: 1,
    processingType: 1,
    productType: 1,
    templateList: {
      template: [
        {
          templateType: '',
          processNum: '',
        },
      ],
    },
    auditStrategy: {
      // video: '',
      // template: '',
      checkType: 1,
    },
    media: '',
    app: '',
    accountType: 'appId',
    accountIds: [],
    channelCodes: [],
    campaignFilter: [
      {
        field: 'media_cost',
        condition: '',
        value: '',
      },
      {
        field: 'create_time',
        condition: 'equal',
        value: '',
      },
      {
        field: 'ad_click_income_roi',
        condition: '',
        value: '',
      },
    ],
    materialFilter: [
      {
        field: '',
        condition: '',
        value: '',
      },
    ],
    pushPlatform: 1,
    selectCatalog: {
      albumName: '',
      albumId: '',
      folder: {
        label: '',
        value: '',
      },
    },
    dir: {
      label: '',
      value: '',
    },
    targetDir: [],
    targetSelectCatalog: [],
    folderFilter: [],
    createdDateRange: [],
    // maxNum: 0,
  };
}

export function getRuleFormValues() {
  return {
    baseRule: '',
    baseRuleTime: '',
    appendRule: '',
    policyName: '',
  };
}

export function getBaseFormSchema() {
  return {
    fields: [
      {
        type: 'viewCard',
        label: '加工策略',
        fields: [
          {
            label: '加工类型',
            name: 'processingType',
            type: 'select',
            format: 'radio',
            source: {
              data: (data, formData) => [
                {
                  label: '原料加工',
                  value: 1,
                  // disabled: formData.startType === 3,
                },
                {
                  label: '去重衍生',
                  value: 2,
                  // disabled: formData.startType !== 3,
                },
              ],
            },
            onChange(value, formData) {
              formData.value.templateList.template = [
                {
                  templateType: '',
                  processNum: '',
                },
              ];
            },
            visibleOn: (_, formData) => {
              return !!formData;
            },
          },
          {
            label: '成品类型',
            name: 'productType',
            type: 'select',
            format: 'radio',
            source: {
              data: (data, formData) => [
                {
                  label: '小说素材',
                  value: 1,
                  // disabled: formData.startType === 3,
                },
                {
                  label: '快应用素材',
                  value: 2,
                  // disabled: formData.startType !== 3,
                },
              ],
            },
            visibleOn: (_, formData) => {
              return !!formData;
            },
            onChange(value, formData) {
              formData.value.templateList.template = [
                {
                  templateType: '',
                  processNum: '',
                },
              ];
            },
          },
          {
            label: '选择加工模板',
            name: 'templateList',
            type: 'object',
            fields: [
              {
                label: '视频',
                name: 'videoType',
                type: 'select',
                format: 'singleSelect',
                span: 7,
                source: {
                  data: (_, formData) => {
                    // 指定文件夹 ---> 解压素材
                    // 三方平台 ---> 滚屏素材
                    if (!formData.templateList.videoType) {
                      formData.templateList.videoType = formData.startType;
                    }
                    if (formData.source === 1 && formData.startType === 1) {
                      return CONSTANT.VideoType;
                    }
                    return CONSTANT.VideoType.slice(0, 1);
                  },
                },
                required: true,
                hideAstrisk: true,
                placeholder: '请选择',
              },
              {
                name: 'processNum',
                label: '生成数量设置',
                type: 'number',
                span: 7,
                required: true,
                min: 1,
                max: 6,
                precision: 0,
              },
            ],
            visibleOn: (_, formData) => {
              return (
                formData.processingType === 1 && formData.productType === 1
              );
            },
          },
          {
            label: '选择加工模板',
            name: 'templateList',
            type: 'object',
            fields: [
              {
                label: '模版',
                name: 'template',
                type: 'custom',
                required: true,
                component: TemplateList,
              },
            ],
            visibleOn: (_, formData) => {
              return (
                formData.processingType === 2 && formData.productType === 2
              );
            },
          },
          {
            label: '选择加工模板',
            name: 'templateList',
            type: 'object',
            fields: [
              {
                label: '视频',
                name: 'videoType',
                type: 'select',
                format: 'singleSelect',
                span: 7,
                source: {
                  data: (_, formData) => {
                    formData.templateList.videoType = '';
                    return [];
                  },
                },
                required: true,
                hideAstrisk: true,
                placeholder: '请选择',
              },
              {
                name: 'processNum',
                label: '生成数量设置',
                type: 'number',
                span: 7,
                required: true,
                min: 1,
                max: 6,
                precision: 0,
              },
            ],
            visibleOn: (_, formData) => {
              return (
                !(
                  formData.processingType === 1 && formData.productType === 1
                ) &&
                !(formData.processingType === 2 && formData.productType === 2)
              );
            },
          },
        ],
      },
      {
        type: 'viewCard',
        label: '审核策略',
        fields: [
          {
            label: '',
            name: 'auditStrategy',
            type: 'object',
            viewType: 'card',
            fields: [
              // {
              //   name: 'video',
              //   label: '视频',
              //   type: 'select',
              //   format: 'singleSelect',
              //   span: 7,
              //   required: true,
              //   source: {
              //     data: CONSTANT.VideoType,
              //   },
              // },
              {
                label: '审核模版',
                name: 'checkType',
                type: 'select',
                format: 'singleSelect',
                span: 7,
                source: {
                  data: CONSTANT.AuditStrategy,
                },
                required: true,
                hideAstrisk: true,
                placeholder: '下拉选择审核模版',
              },
            ],
          },
        ],
      },
      {
        type: 'viewCard',
        label: '推送策略',
        fields: [
          {
            name: 'pushPlatform',
            label: '目标素材库',
            type: 'select',
            format: 'buttonRadio',
            required: true,
            source: {
              data: PushPlatformOptions.filter((item) => item.value !== 3),
            },
            onChange(value, formData) {
              formData.value.selectCatalog.albumName = '';
              formData.value.selectCatalog.albumId = '';
              formData.value.selectCatalog.folder.label = '';
              formData.value.selectCatalog.folder.value = '';

              formData.value.dir.label = '';
              formData.value.dir.value = '';
            },
          },
          {
            label: '优量文件夹',
            name: 'dir',
            placeholder: '请选择',
            type: 'treeSelect',
            select: {
              allowSearch: true,
              labelInValue: true,
              fullPath: true,
              customTitle: (data) => {
                return <span>{data.title}</span>;
              },
            },
            source: {
              valueKey: 'key',
              labelKey: 'path',
              data: trees,
            },
            rules: [
              { required: true, message: '请选择素材目录' },
              {
                validator: (val, cb) => {
                  const { label, value } = val;
                  if (label && value) {
                    cb();
                  } else {
                    cb('请选择素材目录');
                  }
                },
              },
            ],
            visibleOn: (_, formData) => {
              return formData.pushPlatform === 1;
            },
          },
          {
            name: 'selectCatalog',
            label: '创量文件夹',
            type: 'custom',
            component: DirSelect,
            style: { marginBottom: '0' },
            required: true,
            params: {
              disabledCreate: true,
              multiple: false,
            },
            rules: [
              { required: true, message: '请选择素材目录' },
              {
                validator: async (value, cb) => {
                  await nextTick();
                  const {
                    albumId,
                    albumName,
                    folder: { label, value: catalogValue } = {
                      label: '',
                      value: '',
                    },
                  } = value;

                  if (albumId && albumName && catalogValue && label) {
                    cb();
                  } else {
                    cb('请选择素材目录');
                  }
                },
              },
            ],
            visibleOn: (_, formData) => {
              return formData.pushPlatform === 2;
            },
          },
        ],
      },
    ],
  };
}
