/* eslint-disable import/prefer-default-export */

import { defineStore } from 'pinia';
import { usePlayerState } from '@/store/modules/player-state';
import { Track, TrackLineItem } from '@/class/Track';
import type { VideoTrack } from '@/class/videoTrack';
import useEditorConfigStore from '@/store/modules/editor-config';

export const useTrackState = defineStore('trackState', {
  state: () => ({
    trackList: [] as TrackLineItem[],
    selectTrackItem: {
      line: -1,
      index: -1,
    },
    allFiles: [] as any[],
  }),

  getters: {
    selectResource: (state) => {
      if (state.selectTrackItem.line === -1) {
        return null;
      }
      return (
        state.trackList[state.selectTrackItem.line]?.list[
          state.selectTrackItem.index
        ] || null
      );
    },
    hasAiLogoImage: (state) => {
      return state.trackList.some((item) =>
        item.list.some((listItem) => listItem.source.isAiLogo)
      );
    },
  },

  actions: {
    removeTrack(lineIndex: number, itemIndex: number) {
      if (this.trackList[lineIndex].list[itemIndex]?.source?.isMain) {
        return;
      }
      this.trackList[lineIndex].list.splice(itemIndex, 1);
      if (
        this.trackList[lineIndex].list.length === 0 &&
        !this.trackList[lineIndex].main
      ) {
        this.trackList.splice(lineIndex, 1);
      }
      if (this.trackList.length === 1 && this.trackList[0].list.length === 0) {
        this.trackList.splice(0, 1);
      }
    },

    selectTrackById(id: string, type) {
      this.trackList.forEach((item, lineIndex) => {
        if (item.type === type) {
          item.list.forEach((listItem, itemIndex) => {
            if (listItem.id === id) {
              this.setSelectTrackItem({ line: lineIndex, index: itemIndex });
            }
          });
        }
      });
    },

    addTrack(newItem: Track) {
      const videoLines = this.trackList.filter((line) => line.type === 'video');
      const hasMain = videoLines[0]?.list?.some((item) => item.source?.isMain);

      if (newItem.type !== 'video') {
        this.trackList.unshift({
          type: newItem.type,
          list: [newItem],
        });
        // this.selectTrackItem.line = this.trackList.length - 1;
        // this.selectTrackItem.index = 0;
        this.selectTrackItem.line = 0;
        this.selectTrackItem.index = 0;
      } else if ((newItem as VideoTrack)?.source.op) {
        videoLines[0].list.unshift(newItem);
        this.selectTrackItem.line = 0;
        this.selectTrackItem.index = 0;
      } else if ((newItem as VideoTrack)?.source.ed) {
        videoLines[0].list.push(newItem);
        this.selectTrackItem.line = 0;
        this.selectTrackItem.index = videoLines[0].list.length - 1;
      } else if (!hasMain) {
        this.trackList.push({
          type: newItem.type,
          list: [newItem],
        });
        this.selectTrackItem.line = this.trackList.length - 1;
        this.selectTrackItem.index = 0;
      } else {
        videoLines[0].list.forEach((item, index) => {
          if (item.source?.isMain) {
            this.selectTrackItem.line = this.trackList.length - 1;
            videoLines[0].list[index] = newItem;
            this.selectTrackItem.index = index;
          }
        });
      }
    },

    updateTextTrackItem({
      id,
      content,
      fontSize = 40,
    }: {
      id: string;
      content: string;
      fontSize?: number;
    }) {
      this.selectTrackById(id);
      if (this.selectResource) {
        const contentOption = this.selectResource?.source?.contentOption;
        const index = contentOption.findIndex(
          (item) => item === this.selectResource?.source?.content
        );
        contentOption[index] = content;
        this.selectResource.source = {
          ...this.selectResource.source,
          content,
          contentOption,
          fontSize: Math.round(fontSize),
        };
      }
    },

    updateImgTrackItem({ id, width, height }) {
      const currentItem = this.trackList.find((item) => {
        return item?.list?.[0]?.id === id;
      });

      if (currentItem) {
        currentItem.list[0].source.width = width;
        currentItem.list[0].source.height = height;
      }
    },

    updateImgTrackItemAiLogo({ id, isAiLogo }) {
      const currentItem = this.trackList.find((item) => {
        return item?.list?.[0]?.id === id;
      });
      const editConfigStore = useEditorConfigStore();
      if (currentItem) {
        currentItem.list[0].source.isAiLogo = isAiLogo;
        if (isAiLogo) {
          currentItem.list[0].source.stroke = '#000000';
          currentItem.list[0].source.strokeWidth = 0;
          currentItem.list[0].source.opacity = 100;
          currentItem.list[0].source.appearTime.isAllTime = true;
          currentItem.list[0].source.appearTime.begin = 0;
          currentItem.list[0].source.appearTime.end =
            editConfigStore.minimumDuration;
        }
      }
      const activeObj = usePlayerState().canvasInstance.getElementById(id);
      if (activeObj && isAiLogo) {
        activeObj.stroke = '#000000';
        activeObj.strokeWidth = 0;
        activeObj.opacity = 100;
        activeObj.appearTime.isAllTime = true;
        activeObj.appearTime.begin = 0;
        activeObj.appearTime.end = editConfigStore.minimumDuration;
      }
    },
    setAllFiles(files: any[]) {
      this.allFiles = files;
    },

    setSelectTrackItem({ line, index }) {
      this.selectTrackItem.line = line;
      this.selectTrackItem.index = index;
    },
  },
});
