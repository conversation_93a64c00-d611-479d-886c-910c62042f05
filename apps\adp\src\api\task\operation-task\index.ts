import { http } from '@/utils/http';
import { successResWrap } from '@/utils/setup-mock';
// eslint-disable-next-line import/prefer-default-export
export const operationTask = {
  list(data) {
    return http.post('/adplan/operation/list', data);
  },
  detail(data) {
    return http.get('/adplan/operation/detail', data);
  },
  detailTabsList(data) {
    return http.post('/adplan/common/refresh/v1/data/page', data);
  },
  failDetail(data) {
    return http.post('/adplan/common/refresh/v1/data/failDetail', data);
  },
  getOperationType(data) {
    return http.get('/adplan/operation/types/v1', data);
  },
  // 操作对象类型
  getOperationTargetType(data) {
    return http.get('/adplan/operation/target/types/v1', data);
  },

  taskStop(data) {
    return http.get('/adplan/operation/task/stop', data);
  },
  detailStop(data) {
    return http.get('/adplan/operation/detail/stop', data);
  },
  detailList(data) {
    return successResWrap({
      id: 1,
      name: '任务名称1',
      media: '快手',
      rule: '提交规则1',
      projectCount: '11',
      adCount: '22',
      updatedAt: '2024-03-25',
      status: '执行中',
    });
    return http.post('/application/parameters/pages/detail', data);
  },
};

export const otherGdtTask = {
  stopTask(data) {
    return http.post('/adplan/gdt/operation/v1/stop', data);
  },
};
