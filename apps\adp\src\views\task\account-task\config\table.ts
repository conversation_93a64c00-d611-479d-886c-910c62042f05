import { operationTask } from '@/api/task/operation-task';
import useAppId from '@/hooks/app-id';
import {
  platformAccountType,
  AccountStatus,
  batchReplacePlatformAccounts,
  COMPLETED_STATUSES,
  TaskType,
} from '@/views/task/account-task/constants';
import { mediaPlatformMap } from '@/views/promotion/hooks/authorization';
import { h } from 'vue';
import DetailContent from '../components/detail-content.vue';

export function getTableJson(currentPage: platformAccountType) {
  const appId = useAppId();
  const detailsOperation = {
    text: '详情',
    auth: `task_account_${currentPage.value}_detail`,
    props: {
      type: 'text',
    },
    clickActionType: 'drawer',
    linkUrl: '',
    modal: {
      props: {
        title: '详情',
        width: 1280,
        hideCancel: true,
      },
      contentType: 'custom',
      custom: h(DetailContent, {
        currentPage,
      }),
    },
  };
  const stopOperation = {
    text: '终止',
    props: {
      type: 'text',
      // 所有未执行完成的任务（全部维度链接替换完成算完成） || 目前仅支持：账户换投
      disabled: ({ record }: any) => {
        return (
          COMPLETED_STATUSES.has(record.status) ||
          record.taskType !== TaskType.REPLACE
        );
      },
    },
    clickActionType: 'modal',
    modal: {
      props: {
        title: '提示',
        width: 500,
      },
      contentType: 'text',
      text: '是否确认终止全部待执行任务及其明细？',
      action: async (data: any) => {
        await operationTask.taskStop({
          id: data.record.id,
        });
        data.refreshTable();
      },
    },
  };

  const operationColumns = {
    title: '操作',
    dataIndex: 'operations',
    customRender: {
      type: 'operations',
      props: {
        operations: [detailsOperation, stopOperation],
      },
    },
  };

  const commonColumns = [
    {
      title: '任务名称',
      dataIndex: 'taskName',
    },
    {
      title: '操作类型',
      dataIndex: 'taskTypeName',
    },
    {
      title: '提交数量',
      dataIndex: 'operationCount',
    },
    {
      title: '创建时间',
      dataIndex: 'createAt',
    },
    {
      title: '操作结果',
      dataIndex: 'statusName',
      render: ({ record }) => {
        return record.statusName;
      },
    },
  ];

  const statusColumns = batchReplacePlatformAccounts.includes(
    currentPage.mediaType
  )
    ? [
        {
          title: '数量',
          dataIndex: 'executeDetail',
          render: ({ record }) => {
            return record.status === AccountStatus.PartSuccess
              ? record.executeDetail
              : '-';
          },
        },
        {
          title: '结束时间',
          dataIndex: 'updateAt',
        },
      ]
    : [];

  return {
    load: {
      action: async (filter, pagination) => {
        return operationTask.list?.({
          mediaPlatform: mediaPlatformMap[currentPage.mediaType],
          applicationId: appId.value,
          taskSource: 1,
          ...filter,
          ...pagination,
        });
      },
    },
    columns: [...commonColumns, ...statusColumns, operationColumns],
  };
}
