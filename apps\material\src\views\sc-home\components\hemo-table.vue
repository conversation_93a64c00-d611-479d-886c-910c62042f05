<template>
  <div class="home-table">
    <a-table
      class="table-instances"
      :loading="loading"
      :columns="homeColumns"
      :data="tableData"
      :scroll="scroll"
      :scroll-bar="false"
      :pagination="pagination"
      @page-size-change="handlePageSizeChange"
      @page-change="handlePageChange"
    >
      <template #toolName="{ record }">
        <div style="display: flex; align-items: center">
          <span> {{ record.toolName }}</span>
          <a-tooltip
            :content="formatAiUtilsName(record.aiLabels)"
            v-if="record.aiLabels"
          >
            <AI
              class="icon-size"
              :style="{
                width: '14px',
                height: '14px',
                marginLeft: '4px',
              }"
            />
          </a-tooltip>
        </div>
      </template>
      <template #status="{ record }">
        <StatusText :type="record.statusType" hide-text-color>
          {{ record.statusName }}
        </StatusText>
      </template>
      <template #taskStatus="{ record }">
        <a-space size="medium">
          <StatusText hide-dot hide-text-color v-if="record.nameType !== 3">
            总数:{{ record.commitNum || '-' }}
          </StatusText>
          <StatusText type="success" hide-text-color>
            已完成:{{ record.successNum || '-' }}
          </StatusText>
          <StatusText type="success" hide-text-color>
            已提审:{{ record.checkedNum || '-' }}
          </StatusText>
          <StatusText type="error" hide-text-color>
            失败:{{ record.failNum || '-' }}
          </StatusText>
        </a-space>
      </template>
      <template #operations="{ record }">
        <!-- 草稿状态不展示 提审中禁用，已完成可操作-->
        <a-button
          v-if="record.status !== statusTypeEnum.draft"
          @click="handleView(record)"
          type="text"
          :disabled="
            record.status === statusTypeEnum.wait ||
            record.status === statusTypeEnum.terminated
          "
        >
          查看
        </a-button>
        <a-button
          type="text"
          status="danger"
          v-if="record.status === statusTypeEnum.wait"
          @click="handleTerminate(record)"
          :loading="record.terminateLoading"
        >
          终止任务
        </a-button>
        <template v-else>
          <a-button
            v-if="record.status !== statusTypeEnum.draft"
            @click="handleTaskReview({ ...record, materialIds: [] })"
            type="text"
            :disabled="record.successNum === 0"
          >
            提交审核
          </a-button>
        </template>
        <a-button
          v-if="record.status !== statusTypeEnum.draft"
          @click="handleDownload(record)"
          type="text"
          :loading="record.downloadLoading"
          :disabled="record.successNum === 0 && record.checkedNum === 0"
        >
          下载
        </a-button>
      </template>
    </a-table>
    <ViewContentDrawer
      v-model="viewVisible"
      v-bind="viewOptions"
      @refresh="loadData"
    />
    <ReviewTaskModal
      v-model="reviewTaskVisible"
      v-bind="reviewTaskOptions"
      @refresh="loadData"
    />
  </div>
</template>

<script setup lang="tsx">
  import { inject, onMounted, ref } from 'vue';
  import useTablePage from '@/hooks/use-table-page';
  import { Message } from '@arco-design/web-vue';
  import { homeApi } from '@/api/sc-home';
  import {
    homeColumns,
    homeContext,
    nameTypeList,
    statusTypeEnum,
    statusTypeList,
  } from '@/views/sc-home/utils';
  import StatusText from '@/components/status-text/index.vue';
  import useLoading from '@/hooks/loading';
  import useViewTask from '@/views/sc-home/hook/view-task';
  import useReviewTask from '@/views/sc-home/hook/review-task';
  import { formatAiUtilsName } from '@/utils/ai-utils';
  import AI from '@/components/container/resource/svg/AI.svg?component';

  import ViewContentDrawer from './view-content-drawer.vue';
  import ReviewTaskModal from './review-task-modal.vue';

  const {
    pagination,
    currentPage,
    handlePageSizeChange,
    handlePageChange,
    resetPage,
  } = useTablePage(loadData);
  const scroll = {
    x: '100%',
    y: '100%',
  };

  const tableData = ref();

  const homeInstances = inject<any>(homeContext);

  const { loading, setLoading } = useLoading();

  function loadData() {
    if (loading.value) return;
    const search = homeInstances?.getSearch?.() ?? {};
    const params = {
      ...search,
      ...currentPage.value,
    };
    setLoading(true);
    homeApi
      .list(params)
      .then((res) => {
        tableData.value = (res.data?.list || []).map((item) => {
          const statusItem = statusTypeList.find(
            (v) => v.value === item.status
          );
          return {
            ...item,
            toolName:
              nameTypeList.find((v) => v.value === item.nameType)?.label ?? '',
            statusName: statusItem?.label ?? '',
            statusType: statusItem?.type ?? '',
          };
        });
        pagination.total = res.data?.pageInfo?.total || 0;
      })
      .finally(() => {
        setLoading(false);
      });
  }

  // 查看
  const { handleView, viewVisible, viewOptions } = useViewTask();
  // 提审
  const { handleTaskReview, reviewTaskVisible, reviewTaskOptions } =
    useReviewTask();
  // 终止任务
  async function handleTerminate(record) {
    record.terminateLoading = true;
    try {
      await homeApi.terminate({
        taskId: record.id,
      });
      Message.success('终止任务成功');
      loadData();
      record.terminateLoading = false;
    } catch (error) {
      loadData();
      record.terminateLoading = false;
    } finally {
      record.terminateLoading = false;
    }
  }
  const handleDownload = async (record) => {
    record.downloadLoading = true;
    try {
      const { data, code } = await homeApi.download({
        taskId: record.id,
      });
      if (code === 1) {
        Message.success('提交成功');
        record.downloadLoading = false;
      } else {
        Message.error(data.message);
        record.downloadLoading = false;
      }
    } catch (error) {
      record.downloadLoading = false;
    }
  };
  onMounted(() => {
    loadData();
  });
  defineExpose({
    loadData,
    resetPage,
  });
</script>

<style scoped lang="less">
  .home-table {
    overflow: hidden;
    flex: 1;
    padding-bottom: 16px;

    .table-instances {
      height: 100%;
    }
  }
</style>
