import { API } from '@/types/global';
import { http } from '@/utils/http';

const abandonPkgManage = {
  list(data) {
    return http.post('/advancement/abandon-pkg/list', data);
  },
  create(data) {
    return http.post('/advancement/abandon-pkg/add', data);
  },
  remove(data) {
    return http.post('/advancement/abandon-pkg/delete', data);
  },
  batchUpload(data) {
    return http.post('/advancement/abandon-pkg/batch-add', data);
  },
  modify(data) {
    return http.post('/advancement/abandon-pkg/edit', data);
  },
  detail(data) {
    return http.post('/advancement/abandon-pkg/detail', data);
  },
  adSearchList(data) {
    return http.post('/advancement/abandon-pkg/app-store-quick-app-list', data);
  },
};

export default abandonPkgManage;
