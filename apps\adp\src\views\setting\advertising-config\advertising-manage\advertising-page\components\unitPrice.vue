<template>
  <a-space direction="vertical" style="flex: 1">
    <DynamicForm v-model="form" :form-schema="filterSchema" layout="inline">
      <template #footer v-if="isBatch">
        <a-button type="primary" @click="handleApply">应用 </a-button>
      </template>
    </DynamicForm>
    <a-alert
      type="warning"
      v-if="
        currentNumber !== 0 &&
        form.number !== currentNumber &&
        form.number > currentNumber * 1.5
      "
      style="margin-bottom: -8px"
      >超出原数值的50%
    </a-alert>
    <template v-if="isBatch">
      <a-table
        :columns="columns"
        :data="model"
        style="margin-top: 20px"
        :pagination="model?.length > 10 ? pagination : false"
        :scroll="
          model.length > 5
            ? {
                x: '100%',
                y: '366px',
              }
            : null
        "
        :scrollbar="true"
      >
        <template #updatedNumber="{ record }">
          <a-space>
            <a-input-number
              style="width: 120px"
              :min="0"
              :precision="4"
              allow-clear
              disabled
              v-model="record.updatedNumber"
            />
          </a-space>
        </template>
        <template #updatedState="{ record }">
          <div v-if="record.updatedNumber >= 0" class="status">
            <div
              class="tip-icon"
              :class="record.updatedState ? 'fail-tip' : 'success-tip'"
            ></div>
            <div
              class="tip-text"
              :class="record.updatedState ? 'fail-text' : 'success-text'"
              >{{ record.updatedState ? '超过50%' : '成功' }}</div
            >
          </div>
        </template>
      </a-table>
      <div>已选{{ model.length }}个可操作{{ targetName }}</div>
    </template>
  </a-space>
</template>

<script setup lang="ts">
  import { computed, ref, watch } from 'vue';
  import { FormSchema } from '@repo/kola/src/components/dynamic/types/form';
  import { isArray, map, cloneDeep, toNumber, isNaN } from 'lodash';

  const model = defineModel<any>();

  const props = defineProps({
    columns: {
      type: Array,
      default() {
        return [
          {
            title: '广告位ID',
            dataIndex: 'adId',
            width: 200,
          },
          {
            title: '点击单价',
            dataIndex: 'number',
            width: 120,
          },
          {
            title: '修改后点击单价',
            dataIndex: 'updatedNumber',
            slotName: 'updatedNumber',
            width: 150,
          },
          {
            title: '修改结果',
            dataIndex: 'updatedState',
            slotName: 'updatedState',
            width: 100,
          },
        ];
      },
    },
    filterSchema: {
      type: Object,
      default: () => ({
        fields: [
          {
            name: 'number',
            label: '点击单价',
            type: 'number',
            min: 0,
            max: 100,
            precision: 4,
            placeholder: '请输入点击单价',
            rules: [{ required: true, message: '请输入点击单价' }],
          },
        ],
      }),
    },
    targetName: {
      type: String,
      default: '',
    },
  });

  const columns = computed(() => {
    return props.columns;
  });

  const isBatch = ref(isArray(model.value));
  let currentNumber = cloneDeep(model.value);

  const pagination = {
    total: model.value?.length ?? 0,
    pageSize: 10,
    pageNumber: 1,
    showJumper: true,
    showTotal: true,
  };

  const form = ref({
    number: Array.isArray(currentNumber) ? undefined : toNumber(currentNumber),
  });

  watch(
    () => form.value.number,
    (value, oldValue) => {
      if (!isBatch.value) {
        model.value = Number(value);
      }
    }
  );

  watch(
    () => model.value,
    (value, oldValue) => {
      if (oldValue === '') {
        form.value = {
          number: isNaN(toNumber(value)) ? null : toNumber(value),
        };
        currentNumber = value;
      }
      if (!isBatch.value) {
        form.value.number = Number(value);
      }
    },
    { deep: true }
  );

  const handleApply = () => {
    model.value = map(model.value, (item) => {
      return {
        ...item,
        updatedNumber: form.value.number,
        updatedState: getCurStatus(item),
      };
    });
  };

  const getCurStatus = (item) => {
    if (Number(item.number) === 0) return false;
    if (form.value.number > item.number * 1.5) return true;
    return false;
  };
</script>

<style lang="less" scoped>
  .status {
    display: flex;
    align-items: center;

    .tip-icon {
      width: 8px;
      height: 8px;
      border-radius: 20px;
      margin-right: 8px;
    }

    .success-tip {
      background-color: green;
    }

    .fail-tip {
      background-color: red;
    }

    .success-text {
      color: green;
    }

    .fail-text {
      color: red;
    }
  }
</style>
