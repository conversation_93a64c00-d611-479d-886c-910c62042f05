//  小米，OPPO，uc, 趣头条  批量操作
import { syncDataTabDataType } from '@/views/promotion/kuaishou/constants';
import accountCommonApi from '@/api/promotion/common';
import { postbackLinkApi } from '@/api/postback/link';
import { Message } from '@arco-design/web-vue';
import { isEmpty, isNil, some } from 'lodash';
import {
  getCustomConfigFormSchema,
  getCustomSubmitFormData,
} from '@/views/promotion/account/component/authorize-account/components/select-link/form';
import { getCustomFormData } from '@/views/promotion/account/component/authorize-account/constants';
import { useBatchGetQuality } from '@/views/promotion/account/get-quality';
import {
  getBatchExportSchema,
  getReplaceAppField,
} from '@/views/promotion/account/common';
import { MediaType } from '@/views/promotion/hooks/authorization';
import { MediaPlatform } from '@/api/promotion/types';
import account from '@/constants/auth-key/promotion/account';
import { useBatchOwner } from './batch-owner';
import { useBatchAgent } from './batch-agent';

function getBatchOperation({
  getList,
  mediaPlatform,
  updateAuth,
  deleteAuth,
  refreshAuth,
  exportAuth,
  replaceAppConfig = {},
}: {
  getList: () => any;
  mediaPlatform: number;
  updateAuth: string;
  deleteAuth: string;
  refreshAuth?: string;
  exportAuth?: string;
  replaceAppConfig?: any;
}) {
  const batchOperation = [
    {
      text: '批量修改数据更新状态',
      auth: updateAuth,
      props: {
        type: 'primary',
        disabled: false,
        loading: false,
      },
      operationPosition: 'batch',
      clickActionType: 'modal',
      modal: {
        props: {
          title: '批量修改数据更新状态',
          width: 500,
          fullscreen: false,
        },
        contentType: 'form',
        form: {
          formSchema: {
            fields: [
              {
                name: 'isOpen',
                label: '正常更新',
                type: 'switch',
              },
            ],
          },
        },
        action: async (data: any) => {
          await accountCommonApi.batchEdit({
            advertiserIds: data.record,
            isOpen: data.formData.isOpen || 0,
            mediaPlatform,
          });
          Message.success('更新成功');
          data.refreshTable();
        },
      },
    },
    useBatchOwner(mediaPlatform),
    refreshAuth ? getRefreshOperation(refreshAuth, mediaPlatform) : null,
    {
      text: '批量删除',
      auth: deleteAuth,
      props: {
        type: 'outline',
        disabled: (data: any) => {
          const selectRows = data.record.map((id) =>
            getList().find((row) => row.advertiserId === id)
          );
          return some(selectRows, (item) => item?.isOpen);
        },
      },
      operationPosition: 'batch',
      clickActionType: 'modal',
      modal: {
        props: {
          title: '批量删除',
          width: 500,
          fullscreen: false,
        },
        contentType: 'text',
        text: '是否确认删除所选账户与所属人员绑定关系？',
        action: async (data: any) => {
          await accountCommonApi.batchRemove?.({
            advertiserIds: data.record,
            mediaPlatform,
          });
          Message.success('删除成功');
          data.refreshTable();
        },
      },
    },
    useBatchGetQuality({
      platformCode: mediaPlatform,
    }),
    useBatchAgent(mediaPlatform),
    !isEmpty(replaceAppConfig)
      ? getReplaceAppField({
          getList,
          ...replaceAppConfig,
        })
      : null,
    getReplaceAppField({
      getList,
      platformCode: MediaPlatform.WEIBO,
      mediaTypeCode: MediaType.WEI_BO,
      config: {
        auth: account.ACCOUNT_WEIBO_BATCH_REPLACE_APP,
      },
    }),
    exportAuth
      ? getBatchExportSchema({
          config: {
            auth: exportAuth,
          },
          getQuery(data) {
            return {
              advertiserIds: data.record,
              platform: mediaPlatform,
              applicationId: window.$appInfo?.id,
            };
          },
        })
      : null,
  ];
  return batchOperation.filter((item) => {
    return !isNil(item);
  });
}
export default getBatchOperation;

// 百度 华为 vivo 批量操作中 刷新账户信息

function getRefreshOperation(refreshAuth, mediaPlatform) {
  return {
    text: '刷新账户信息',
    auth: refreshAuth,
    props: {
      type: 'outline',
    },
    operationPosition: 'batch',
    clickActionType: 'action',
    action: async (ref: any) => {
      const data = {
        mediaPlatform,
        dataType: syncDataTabDataType.ACCOUNT,
        idList: ref.record,
        applicationId: window.$appInfo?.id,
      };
      await accountCommonApi.syncData(data);
      ref.refreshTable();
      Message.success('操作成功');
    },
  };
}

export function getCustomLinkConfig(
  callback?,
  editRecord?,
  isUpDate?,
  mediaPlatform?,
  curApplicationId?
) {
  return {
    text: '更改链接',
    // auth: landingAuth.POST_BACK_LANDING_STYLE_CREATE,
    props: {
      type: 'primary',
      disabled: false,
      loading: false,
    },
    clickActionType: 'drawer',
    modal: {
      props: {
        title: '自定义配置链接',
        width: 900,
        fullscreen: false,
      },
      contentType: 'form',
      form: {
        formSchema: getCustomConfigFormSchema(
          mediaPlatform,
          true,
          curApplicationId
        ),
      },
      getDefaultValue: getCustomFormData(),
      action: async ({ formData, refreshTable }: any) => {
        const data = getCustomSubmitFormData(formData, curApplicationId);
        await postbackLinkApi.create?.({
          ...data,
          advertiserId: editRecord?.advertiserId,
        });

        if (callback) {
          isUpDate.value = true;
          callback();
        }
        Message.success('更新链接成功');
        refreshTable();
      },
    },
  };
}

export const getTail = ({
  configFun,
  buttonText,
}: {
  configFun: any;
  buttonText?;
}) => {
  return {
    type: 'DButton',
    position: 'outer',
    props: (getSourceData) => {
      const callback = () => {
        getSourceData?.();
      };
      const config = configFun?.(callback);
      config.props.type = 'text';
      if (buttonText) {
        config.text = buttonText;
      }
      return config;
    },
  };
};

export function refreshTableData(dynamicPageRef) {
  dynamicPageRef.value?.refreshTable();
}
