import { VideoTrack } from '@/class/VideoTrack';
import { usePlayerState } from '@/store/modules/player-state';
import {
  durationHasChanged,
  epAndEdChanged,
  getVideoTrack,
} from '@/utils/track-utils';
import { storeToRefs } from 'pinia';
import { computed, ref, watch } from 'vue';

const playerStore = usePlayerState();

const {
  playStatus,
  currentTime,
  currentClickTime,
  duration,
  opDuration,
  mainDuration,
} = storeToRefs(playerStore);
const usePlayer = () => {
  const opUrl = computed(() => {
    const opTrack = getVideoTrack('op');
    playerStore.setOpUrl(opTrack?.source?.url ?? '');
    return opTrack?.source?.url ?? '';
  });

  const edUrl = computed(() => {
    const edTrack = getVideoTrack('ed');
    playerStore.setEdUrl(edTrack?.source?.url ?? '');
    return edTrack?.source?.url ?? '';
  });

  const mainUrl = computed(() => {
    const mainTrack = getVideoTrack('isMain');
    playerStore.setMainUrl(mainTrack?.source?.url ?? '');
    return mainTrack?.source?.url ?? '';
  });

  const updateItemDuration = ({
    start,
    end,
    type,
  }: {
    start?: number;
    end?: number;
    type: 'op' | 'ed' | 'isMain';
  }) => {
    const track = getVideoTrack(type);
    if (track) {
      const originStart = track.source?.start;
      const originEnd = track.source?.end;
      const durationConfig = track.source?.durationConfig;
      const op = durationConfig?.removeOpEd?.op || 0;
      const ed = durationConfig?.removeOpEd?.ed || 0;
      if (type === 'isMain') {
        const updateStart = () => {
          if (durationHasChanged()) {
            return originStart;
          }
          if (epAndEdChanged()) {
            return op;
          }
          return start;
        };

        const updateEnd = () => {
          if (durationHasChanged()) {
            return originEnd;
          }
          if (epAndEdChanged()) {
            return (end || 0) - ed;
          }

          return end;
        };
        track.source = {
          ...track.source,
          start: updateStart(),
          end: updateEnd(),
        };
      } else {
        track.source = {
          ...track.source,
          start: originStart || start,
          end: originEnd || end,
        };
      }
    }
  };

  // 播放器控制逻辑
  const mainVideo = () =>
    document.getElementById('mainVideo') as HTMLVideoElement;
  const opVideo = () => document.getElementById('opVideo') as HTMLVideoElement;
  const edVideo = () => document.getElementById('edVideo') as HTMLVideoElement;
  const maskVideo = () =>
    document.getElementById('maskVideo') as HTMLVideoElement;
  const mainControl = ref(true); // 主视频的控制：按照去头去尾后的时间播放

  const handlePause = () => {
    mainControl.value = true;
    maskVideo()?.pause();
    mainVideo()?.pause();
    opVideo()?.pause();
    edVideo()?.pause();
  };

  watch(
    [playStatus, currentTime],
    () => {
      const mainVideoTrack = getVideoTrack('isMain') as VideoTrack;
      const mainStart = mainVideoTrack?.source.start || 0;

      const setMainVideoTime = () => {
        requestAnimationFrame(() => {
          // if (mainControl.value) {
          if (maskVideo()) {
            maskVideo().currentTime = mainStart;
          }
          mainVideo().currentTime = mainStart; // 使用 requestAnimationFrame 确保准确
          // mainControl.value = false;
          // }
        });
      };

      if (playStatus.value === 'play') {
        maskVideo()?.play();
        if (currentTime.value >= duration.value) {
          handlePause();
        } else if (currentTime.value >= opDuration.value + mainDuration.value) {
          setMainVideoTime();
          playerStore.setPlayingKey('ed');
          edVideo()?.play();
        } else if (currentTime.value >= opDuration.value) {
          playerStore.setPlayingKey('main');
          mainVideo()?.play();
          // setMainVideoTime();
          if (edVideo()) {
            edVideo().currentTime = 0;
          }
        } else {
          setMainVideoTime();
          playerStore.setPlayingKey('op');
          if (edVideo()) {
            edVideo().currentTime = 0;
          }
          opVideo()?.play();
        }
      } else {
        handlePause();
      }
    },
    { immediate: true }
  );

  watch(currentClickTime, () => {
    const mainVideoTrack = getVideoTrack('isMain') as VideoTrack;
    const mainStart = mainVideoTrack?.source.start || 0;

    const setMaskVideoTime = (time) => {
      if (maskVideo()) {
        maskVideo().currentTime = time;
      }
    };

    if (currentClickTime.value >= duration.value) {
      handlePause();
    } else if (
      currentClickTime.value >=
      opDuration.value + mainDuration.value
    ) {
      playerStore.setPlayingKey('ed');
      if (edVideo()) {
        const time =
          currentClickTime.value - opDuration.value - mainDuration.value;
        edVideo().currentTime = time;
        setMaskVideoTime(time);
      }
    } else if (currentClickTime.value >= opDuration.value) {
      playerStore.setPlayingKey('main');
      if (mainVideo()) {
        const time = currentClickTime.value - opDuration.value + mainStart;
        mainVideo().currentTime = time;
        setMaskVideoTime(time);
      }
    } else {
      playerStore.setPlayingKey('op');
      if (opVideo()) {
        const time = currentClickTime.value;
        opVideo().currentTime = time;
        setMaskVideoTime(time);
      }
    }
  });

  return {
    opUrl,
    edUrl,
    updateItemDuration,
    getVideoTrack,
    mainUrl,
  };
};

export default usePlayer;
