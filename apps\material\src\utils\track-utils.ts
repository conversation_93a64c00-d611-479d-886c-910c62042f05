/* eslint-disable import/prefer-default-export */

import { VideoTrack } from '@/class/VideoTrack';
import { TextTrack } from '@/class/TextTrack';
import { ImageTrack } from '@/class/ImageTrack';
import { useTrackState } from '@/store/modules/track-state';
import { usePlayerState } from '@/store/modules/player-state';
import useEditorConfigStore from '@/store/modules/editor-config';
import { Message } from '@arco-design/web-vue';
import { storeToRefs } from 'pinia';

export const formatTime = (time) => {
  const totalSeconds = time;
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = Math.floor(totalSeconds % 60);
  const microseconds = Math.floor((totalSeconds % 1) * 100); // 取两位小数作为百分之一秒

  return `${hours.toString().padStart(2, '0')}:${minutes
    .toString()
    .padStart(2, '0')}:${seconds.toString().padStart(2, '0')}:${microseconds
    .toString()
    .padStart(2, '0')}`;
};

const trackStore = useTrackState();
const playerStore = usePlayerState();
const editConfigStore = useEditorConfigStore();
const VIDEO_NAME = {
  op: '片头',
  ed: '片尾',
  isMain: '主视频',
};

export const addVideoTrack = (url, type, duration = 0) => {
  const op = type === 'op';
  const ed = type === 'ed';
  const isMain = type === 'isMain';
  trackStore.addTrack(
    new VideoTrack({
      url,
      name: VIDEO_NAME[type],
      op,
      ed,
      isMain,
      format: 'mp4',
      start: 0,
      end: duration,
      durationConfig: isMain
        ? {
            mode: 'cuttingDuration',
            cuttingDuration: {
              type: 1, // 1片头往后裁.2片尾往前裁
              time: 0, // 视频时长变成X秒
            },
            removeOpEd: {
              op: 0, // 片头
              ed: 0, // 片尾
            },
          }
        : undefined,
      adjustConfig: isMain
        ? {
            brightRange: [0, 0],
            sharpeRange: [0, 0],
            contrastRange: [0, 0],
            saturationRange: [0, 0],
            fpsRange: [30, 60],
            bitRange: [1000, 6000],
            maxSize: 500,
          }
        : undefined,
    })
  );
};

export const addTextTrack = (texItem: {
  fontFamily?: string;
  fontSize?: number;
  color: string;
  stroke?: string;
  content: string;
  strokeWidth?: number;
  position?: {
    axisX: number;
    axisY: number;
  };
  appearTime?: {
    isAllTime: boolean;
    begin: number;
    end: number;
  };
}) => {
  const textTracks = trackStore.trackList.filter(
    (track) => track.type === 'text'
  );

  if (textTracks.length >= 10) {
    Message.error('文字轨道最多添加10个');
    return;
  }
  const {
    fontFamily,
    color,
    stroke,
    content,
    strokeWidth,
    position,
    fontSize,
    appearTime,
  } = texItem;

  const textTrack = new TextTrack({
    fontFamily: fontFamily || '',
    fill: color,
    stroke: stroke || '',
    strokeWidth: strokeWidth || 0,
    fontSize: fontSize || 40,
    isRandom: false,
    appearTime: {
      isAllTime: appearTime?.isAllTime ?? true,
      begin: appearTime?.begin ?? 0,
      end: appearTime?.end ?? editConfigStore.minimumDuration,
    },
    contentOption: [content],
    content,
    position,
  });

  trackStore.addTrack(textTrack);

  playerStore.canvasInstance?.addText(content, {
    ...textTrack.source,
    id: textTrack.id,
  });
  playerStore.pause();
};

export const addImageTrack = (imgItem: {
  url: string;
  position?: {
    axisX: number;
    axisY: number;
  };
  scale?: {
    scaleWidth: number;
    scaleHeight: number;
  };
  isAiLogo?: boolean;
  appearTime?: {
    isAllTime: boolean;
    begin: number;
    end: number;
  };
  opacity?: number;
  stroke?: string;
  strokeWidth?: number;
}) => {
  const imgTracks = trackStore.trackList.filter(
    (item) => item.type === 'image'
  );
  if (imgTracks.length >= 10) {
    Message.error('图片轨道最多添加10个');
    return;
  }
  const {
    url,
    position,
    scale,
    isAiLogo,
    appearTime,
    opacity,
    stroke,
    strokeWidth,
  } = imgItem;

  const imgTrack = new ImageTrack({
    url,
    name: '图片',
    appearTime: {
      isAllTime: appearTime?.isAllTime ?? true,
      begin: appearTime?.begin ?? 0,
      end: appearTime?.end ?? editConfigStore.minimumDuration,
    },
    width: 0,
    height: 0,
    format: 'jpg/png',
    opacity: opacity || 100,
    stroke: stroke || '#000000',
    strokeWidth: strokeWidth || 0,
    position,
    scale,
    isAiLogo: isAiLogo || false,
  });
  trackStore.addTrack(imgTrack);
  playerStore.canvasInstance?.addImage(url, {
    ...imgTrack.source,
    id: imgTrack.id,
  });
  playerStore.pause();
};

export const getVideoTrack = (type) => {
  const videoLine = trackStore.trackList.filter(
    (track) => track.type === 'video'
  );
  const track = videoLine[0]?.list.find((item) => item.source[type] === true);
  return track;
};

export const clearTrack = () => {
  updateDuration();
  playerStore.canvasInstance?.canvas.clear();
  editConfigStore.setIsRandomVideoParams(false);
  editConfigStore.setIsRandomImg(false);
  trackStore.trackList = trackStore.trackList.filter(
    (trackItem) => trackItem.type === 'video'
  );

  // 对于视频轨道，只保留主视频，删除片头和片尾, 重置主视频轨道信息
  trackStore.trackList.forEach((trackItem) => {
    if (trackItem.type === 'video') {
      trackItem.list = trackItem.list.filter((track) => track.source?.isMain);
      trackItem.list[0].source = {
        ...trackItem.list[0].source,
        durationConfig: {
          mode: 'cuttingDuration',
          cuttingDuration: {
            type: 1, // 1片头往后裁.2片尾往前裁
            time: 0, // 视频时长变成X秒
          },
          removeOpEd: {
            op: 0, // 片头
            ed: 0, // 片尾
          },
        },
        adjustConfig: {
          brightRange: [0, 0],
          sharpeRange: [0, 0],
          contrastRange: [0, 0],
          saturationRange: [0, 0],
          fpsRange: [30, 60],
          bitRange: [1000, 6000],
          maxSize: 500,
        },
        start: 0,
        end: playerStore.mainOriginDuration,
      };
    }
  });
};

const loadFont = (name, url) => {
  return new Promise((resolve, reject) => {
    const font = new FontFace(name, `url(${url})`);
    font
      .load()
      .then((loadedFont) => {
        document.fonts.add(loadedFont);
        resolve(loadedFont);
      })
      .catch((error) => {
        Message.error(`${name}字体加载失败`);
        reject(error);
      });
  });
};

export const updateDuration = () => {
  playerStore.setOpDuration(0);
  playerStore.setEdDuration(0);
  playerStore.setMainDuration(playerStore.mainOriginDuration);
  playerStore.setDuration();
  playerStore.setPlayingKey('main');
};

export const applyTemplate = async (item) => {
  clearTrack();
  const { timeSlotConfig, textConfig, imgConfig } = item;
  const { op: opUrl, ed: edUrl } = timeSlotConfig;

  if (opUrl) {
    addVideoTrack(opUrl, 'op');
    playerStore.setPlayingKey('op');
  }
  if (edUrl) {
    addVideoTrack(edUrl, 'ed');
  }

  // eslint-disable-next-line no-restricted-syntax
  for (const textItem of textConfig || []) {
    const {
      stroke,
      fill,
      contentOption,
      strokeWidth,
      fontFamily,
      position,
      fontSize,
    } = textItem;

    const fontName = editConfigStore.getFontName(fontFamily);
    // eslint-disable-next-line no-await-in-loop
    await loadFont(fontName, fontFamily);

    const textPosition = {
      axisX: (position?.axisX ?? 0) * playerStore.globalScale,
      axisY: (position?.axisY ?? 0) * playerStore.globalScale,
    };

    addTextTrack({
      content: contentOption[0],
      color: fill,
      stroke,
      fontFamily: fontName,
      strokeWidth,
      position: textPosition,
      fontSize: fontSize * playerStore.globalScale,
    });
  }
  imgConfig.imgOption?.forEach((option) => {
    const imgPosition = {
      axisX: (option.position?.axisX ?? 0) * playerStore.globalScale,
      axisY: (option.position?.axisY ?? 0) * playerStore.globalScale,
    };
    addImageTrack({
      url: option.url,
      position: imgPosition,
      scale: {
        scaleWidth: option.width * playerStore.globalScale ?? 0,
        scaleHeight: option.height * playerStore.globalScale ?? 0,
      },
    });
  });
  editConfigStore.setIsRandomImg(imgConfig.isRandom);
};

export const replaceMainVideoTrack = (url) => {
  trackStore.trackList.forEach((track) => {
    if (track.type === 'video') {
      track.list.forEach((list) => {
        if (list.source?.isMain) {
          list.source.url = url;
        }
      });
    }
  });
};

// 判断时长是否有变化
export const durationHasChanged = () => {
  const mainTrack = getVideoTrack('isMain');
  const durationConfig = mainTrack?.source.durationConfig;

  if (durationConfig.mode === '' || durationConfig.mode === 'removeOpEd') {
    return false;
  }
  if (
    durationConfig.mode === 'cuttingDuration' &&
    durationConfig.cuttingDuration?.time === 0
  ) {
    return false;
  }
  return true;
};

// 去头尾是否有变化
export const epAndEdChanged = () => {
  const mainTrack = getVideoTrack('isMain');
  const durationConfig = mainTrack?.source.durationConfig;

  if (durationConfig.mode === '' || durationConfig.mode === 'cuttingDuration') {
    return false;
  }

  if (
    durationConfig.mode === 'removeOpEd' &&
    durationConfig.removeOpEd?.ed === 0 &&
    durationConfig.removeOpEd.op === 0
  ) {
    return false;
  }
  return true;
};
const updateCanvasItemDuration = (start, end) => {
  trackStore.trackList.forEach((track) => {
    if (track.type !== 'video') {
      const trackStart = track.list[0]?.source?.appearTime.begin;
      const trackEnd = track.list[0]?.source?.appearTime.end;
      const isAllTime = track.list[0]?.source?.appearTime.isAllTime;
      track.list[0].source.appearTime = {
        isAllTime,
        begin: trackStart,
        end: trackEnd > end ? end : trackEnd,
      };
    }
  });
};
const { mainOriginDuration } = storeToRefs(playerStore);
export const applyDetail = async (item) => {
  clearTrack();
  const {
    timeSlotConfig,
    textConfig,
    imgConfig,
    materialSize,
    backgroundResize,
    durationConfig,
    adjustConfig,
    newLogoConfig,
  } = item;
  const { op: opUrl, ed: edUrl } = timeSlotConfig;
  editConfigStore.setSizeBackType({ materialSize, backgroundResize });
  const mainTrack = getVideoTrack('isMain');
  if (mainTrack) {
    mainTrack.source.durationConfig = durationConfig;
    mainTrack.source.adjustConfig = adjustConfig;

    const formData = durationConfig;

    if (formData.mode === 'cuttingDuration') {
      const start =
        formData.cuttingDuration.type === 1
          ? 0
          : mainOriginDuration.value - formData.cuttingDuration.time;

      const end =
        formData.cuttingDuration.type === 1
          ? formData.cuttingDuration.time
          : mainOriginDuration.value;
      mainTrack.source = {
        ...mainTrack.source,
        start,
        end: end || mainOriginDuration.value,
        durationConfig: {
          mode: 'cuttingDuration',
          cuttingDuration: {
            time: formData.cuttingDuration.time,
            type: formData.cuttingDuration.type,
          },
          removeOpEd: {
            op: 0,
            ed: 0,
          },
        },
      };
      formData.removeOpEd = {
        op: 0,
        ed: 0,
      };
      playerStore.setMainDuration(
        formData.cuttingDuration.time || mainOriginDuration.value
      );
      updateCanvasItemDuration(start, end);
    } else {
      const start = formData.removeOpEd.op;
      const end = mainOriginDuration.value - formData.removeOpEd.ed;
      mainTrack.source = {
        ...mainTrack.source,
        start,
        end,
        durationConfig: {
          mode: 'removeOpEd',
          removeOpEd: formData.removeOpEd,
          cuttingDuration: {
            time: 0,
            type: 1,
          },
        },
      };
      updateCanvasItemDuration(0, end - start);

      playerStore.setMainDuration(
        mainOriginDuration.value -
          formData.removeOpEd.ed -
          formData.removeOpEd.op
      );
    }

    // 更新画布元素的起止时间

    playerStore.setDuration();
  }
  if (opUrl) {
    addVideoTrack(opUrl, 'op');
    playerStore.setPlayingKey('op');
  }
  if (edUrl) {
    addVideoTrack(edUrl, 'ed');
  }
  const onlyAiImg = !textConfig.length;
  if (onlyAiImg) {
    textConfig.push({
      stroke: '#000000',
      fill: '#000000',
      contentOption: [''],
      strokeWidth: 0,
      fontFamily:
        'https://cy-go-projects-test-tactics-http.ghfkj.cn/projects/material-craft/font-file/94.woff2',
    });
  }
  // eslint-disable-next-line no-restricted-syntax
  for (const textItem of textConfig || []) {
    const {
      stroke,
      fill,
      contentOption,
      strokeWidth,
      fontFamily,
      position,
      fontSize,
      appearTime,
    } = textItem;

    const fontName = editConfigStore.getFontName(fontFamily);
    // eslint-disable-next-line no-await-in-loop
    await loadFont(fontName, fontFamily);

    const textPosition = {
      axisX: (position?.axisX ?? 0) * playerStore.globalScale,
      axisY: (position?.axisY ?? 0) * playerStore.globalScale,
    };

    addTextTrack({
      content: contentOption[0],
      color: fill,
      stroke,
      fontFamily: fontName,
      strokeWidth,
      position: textPosition,
      fontSize: fontSize * playerStore.globalScale,
      appearTime,
    });
  }
  if (newLogoConfig) {
    imgConfig.isRandom = false;
    imgConfig.imgOption.push({
      ...newLogoConfig,
      isAiLogo: false,
      isOpen: false,
    });
  }
  imgConfig.imgOption?.forEach((option) => {
    const imgPosition = {
      axisX: (option.position?.axisX ?? 0) * playerStore.globalScale,
      axisY: (option.position?.axisY ?? 0) * playerStore.globalScale,
    };
    addImageTrack({
      url: option.url,
      position: imgPosition,
      scale: {
        scaleWidth: option.width * playerStore.globalScale ?? 0,
        scaleHeight: option.height * playerStore.globalScale ?? 0,
      },
      appearTime: option.appearTime,
      opacity: option.opacity,
      stroke: option.stroke,
      strokeWidth: option.strokeWidth,
    });
  });
  if (onlyAiImg) {
    const { canvasInstance } = storeToRefs(playerStore);
    const id = trackStore.trackList.find((track) => track.type === 'text')
      ?.list[0]?.id;
    const obj = canvasInstance.value?.getElementById(id);
    if (obj) {
      canvasInstance.value?.canvas.remove(obj);
      canvasInstance.value?.canvas.renderAll();
      const type = 'text';
      let tLineIndex = -1;
      let tItemIndex = -1;
      trackStore.trackList.forEach((target: any, lineIndex: number) => {
        if (target.type === type) {
          target.list.forEach((listItem: any, itemIndex: number) => {
            if (listItem.id === id) {
              tLineIndex = lineIndex;
              tItemIndex = itemIndex;
            }
          });
        }
      });
      trackStore.removeTrack(tLineIndex, tItemIndex);
    }
  }
  editConfigStore.setIsRandomImg(imgConfig.isRandom);
};
