import { computed, nextTick, ref } from 'vue';
import { uniq, uniqBy } from 'lodash';

function useCheckAll(props) {
  const showAll = computed(() => {
    return props.field.select?.showAll ?? false;
  });
  const indeterminate = ref(false);
  const checkedAll = ref(false);

  function changeAll({
    flag,
    modelValue,
    sourceData,
    labelKey,
    valueKey,
    inputText,
    // 如果值是对象类型，需要传入这个字段，用于去重
    objectValueKey = '',
  }) {
    if (inputText) {
      const selectValue = sourceData.value
        .filter((option) => {
          return option[labelKey.value]
            .toLowerCase()
            .includes(inputText.toLowerCase());
        })
        .map((item) => item[valueKey.value]);
      const result = [...modelValue.value, ...selectValue];
      modelValue.value = objectValueKey
        ? uniqBy(result, objectValueKey)
        : uniq(result);
      nextTick(() => {
        toggleIndeterminate(modelValue.value, sourceData);
      });
    } else {
      indeterminate.value = false;
      if (flag) {
        checkedAll.value = true;
        modelValue.value = sourceData.value.map(
          (item: any) => item[valueKey.value]
        );
      } else {
        checkedAll.value = false;
        modelValue.value = [];
      }
    }
  }

  function toggleIndeterminate(values, sourceData) {
    if (values.length === sourceData.value.length) {
      checkedAll.value = true;
      indeterminate.value = false;
    } else if (values.length === 0) {
      checkedAll.value = false;
      indeterminate.value = false;
    } else {
      checkedAll.value = false;
      indeterminate.value = true;
    }
  }

  return {
    showAll,
    indeterminate,
    checkedAll,
    changeAll,
    toggleIndeterminate,
  };
}

export default useCheckAll;
