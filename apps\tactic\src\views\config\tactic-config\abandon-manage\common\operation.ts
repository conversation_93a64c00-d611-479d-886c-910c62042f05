import abandonPkgManage from '@/api-v1/config/tactic-config/abandon-manage';
import { BRAND } from '@/constants/enum';

export function getFormSchema(type) {
  return {
    fields: [
      {
        label: '厂商',
        name: 'brands',
        type: 'select',
        format: 'multipleSelect',
        required: true,
        select: {
          allowSearch: true,
          allowClear: true,
        },
        source: {
          labelKey: 'title',
          valueKey: 'key',
          data: BRAND,
        },
      },
      {
        label: '命中关键词',
        name: 'keyword',
        type: 'textarea',
        required: true,
        placeholder: '多个关键词以,分隔',
        setter: (val: string) => {
          return val.trim().replace('，', ',').replace(/\s+/g, '');
        },
      },
    ],
  };
}

export function getFormData() {
  return {
    keyword: '',
    brands: [],
  };
}

// type: 1-直接遗弃 2-曝光后遗弃
function getOperation(type, addAuthKey, deleteAuthKey) {
  return {
    create: {
      auth: addAuthKey,
      action: async (data) => {
        return abandonPkgManage.create?.({ ...data, type });
      },
      form: {
        formSchema: getFormSchema(type),
        defaultFormData: getFormData(),
      },
      ui: {
        width: 650,
      },
    },
    remove: {
      auth: deleteAuthKey,
      action: (data) => {
        return abandonPkgManage.remove?.({ ids: [data.id], type });
      },
    },
    batchUpload: {
      auth: addAuthKey,
      templateName: type === 1 ? '直接遗弃包.xlsx' : '曝光后遗弃包.xlsx',
      action: (data) => {
        data.append('type', type);
        return abandonPkgManage.batchUpload?.(data);
      },
    },
  };
}

export default getOperation;
