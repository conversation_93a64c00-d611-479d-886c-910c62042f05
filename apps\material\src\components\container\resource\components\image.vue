<template>
  <div>
    <div class="header">
      <div class="title">图片</div>
      <a-upload
        action="/"
        :show-file-list="false"
        v-model:file-list="fileList"
        :auto-upload="false"
        multiple
        :on-before-upload="onBeforeUpload"
        ref="uploadRef"
        :accept="ACCEPT.join(',')"
        @change="handleChange"
      >
        <template #upload-button>
          <a-button type="secondary">添加图片</a-button>
        </template>
      </a-upload>
    </div>
    <div class="bar">
      <a-button type="text">推荐</a-button>
    </div>
    <div class="content">
      <img
        class="img-item"
        v-for="item in IMG_TEMPLATE"
        :key="item"
        @click="addTrackItem(item)"
        :src="item"
      />
    </div>
  </div>
  <LoadingMask :loading="uploadLoading" />
</template>

<script setup lang="ts">
  import { Message } from '@arco-design/web-vue';
  import { ref } from 'vue';
  import LoadingMask from '@/components/loading-mask/index.vue';
  import { addImageTrack } from '@/utils/track-utils';
  import { useTrackState } from '@/store/modules/track-state';
  import { getUploadAction } from '../../upload/utils';

  const LIMIT = 10;
  const ACCEPT = ['.jpg', '.jpeg', '.png', '.gif'];
  const fileList = ref<File[]>([]);
  const imgList = ref<
    { url: string; style: { width: number; height: number } }[]
  >([]);
  const uploadLoading = ref(false);

  const trackStore = useTrackState();

  // function handleExceedLimit() {
  //   Message.warning(`最多只能上传${LIMIT}个文件`);
  // }

  function onBeforeUpload(file) {
    const imgLength = trackStore.trackList.filter(
      (item) => item.type === 'image'
    )?.length;
    if (imgLength >= LIMIT) {
      Message.error('图片轨道最多添加10个');
      return false;
    }
    const { name } = file;
    const fileExtension = name.split('.').pop().toLowerCase();
    if (!ACCEPT.includes(`.${fileExtension}`)) {
      Message.warning(`${name}文件格式不正确`);
      return false;
    }
    uploadLoading.value = true;
    return true;
  }

  const handleChange = async (_uploadedFiles: any[], fileItem: any) => {
    const { query, uploadAction } = getUploadAction({
      file: fileItem?.file,
      filename: fileItem.name,
    });
    const res = await uploadAction(query);
    imgList.value.push(res?.url);
    addTrackItem(res?.url);
    if (imgList.value.length === fileList.value.length) {
      uploadLoading.value = false;
    }
  };

  const IMG_TEMPLATE = [
    'https://cy-go-projects-test-tactics-http.ghfkj.cn/projects/material-craft/source-video/2024-08-29/caicai.png',
    'https://cy-go-projects-test-tactics-http.ghfkj.cn/projects/material-craft/source-video/2024-08-29/haoshiduo.png',
    'https://cy-go-projects-test-tactics-http.ghfkj.cn/projects/material-craft/source-video/2024-08-29/kanjia.png',
  ];

  const addTrackItem = (url) => {
    addImageTrack({ url });
  };
</script>

<style scoped lang="less">
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
  }

  .bar {
    border-bottom: 1px solid rgb(0 0 0 / 12%);
  }

  .content {
    padding: 16px;
  }

  .img-item {
    width: 80px;
    margin: 0 8px;
    cursor: pointer;
    height: 80px;
    object-fit: contain;

    &:hover {
      border: 2px solid #1890ff;
      scale: 1.1;
    }
  }

  .upload-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgb(0 0 0 / 50%);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>
