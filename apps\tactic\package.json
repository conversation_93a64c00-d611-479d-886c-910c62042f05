{"name": "smp_dmp_admin", "description": "策略管理系统", "version": "1.0.0", "private": true, "author": "yimin", "license": "MIT", "scripts": {"dev": "vite --config ./config/vite.config.dev.ts", "build": "vite build --config ./config/vite.config.prod.ts", "build:hw-prod": "VITE_MODE=production pnpm run build", "build:hw-test": "VITE_MODE=testing pnpm run build", "build:hw-test1": "VITE_MODE=testing pnpm run build", "report": "cross-env REPORT=true npm run build", "preview": "npm run build && vite preview --host", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "lint-staged": "npx lint-staged", "create-page": "node ./scripts/create-page/index.js"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["prettier --write", "eslint --fix"], "*.vue": ["stylelint --fix", "prettier --write", "eslint --fix"], "*.{less,css}": ["stylelint --fix", "prettier --write"]}, "dependencies": {"@repo/eslint-config": "workspace:*", "@repo/kola": "workspace:*", "@repo/sdk": "workspace:*", "@repo/seal": "workspace:*", "@repo/typescript-config": "workspace:*", "ali-oss": "^6.23.0", "lottie-web": "^5.13.0", "pinyin-pro": "^3.20.4", "vuedraggable": "^4.1.0"}, "engines": {"node": ">=14.0.0"}}