import marketTacticsAuth<PERSON>ey from '@/constants/auth-key/tactics/market-tactics';
import gameMarketTacticsAuthKey from '@/constants/auth-key/tactics/game/market-tactics';
import LandingPage from '@/components/icons/landing-page.vue';
import { DEFAULT_LAYOUT } from '../base';
import { AppRouteRecordRaw } from '../types';

const APPLICATION: AppRouteRecordRaw = {
  path: '/tactics',
  name: 'tactics',
  component: DEFAULT_LAYOUT,
  meta: {
    title: '策略',
    requiresAuth: true,
    icon: 'icon-bulb',
    order: 2,
  },
  children: [
    {
      path: '/tactics/market-tactics',
      name: 'tacticsMarketTactics',
      component: () => import('@/views/tactics/market-tactics/index.vue'),
      meta: {
        title: '营销策略',
        requiresAuth: true,
        roles: ['admin'],
        icon: 'icon-archive',
      },
    },
    {
      path: '/tactics/new-market-tactics',
      name: 'tacticsNewMarketTactics',
      component: () => import('@/views/tactics/new-market-tactics/index.vue'),
      meta: {
        title: '新营销策略',
        requiresAuth: true,
        roles: ['admin'],
        icon: 'icon-archive',
      },
    },
    {
      path: '/tactics/new-market-tactics/config',
      name: 'tacticsNewMarketTacticsConfig',
      component: () =>
        import('@/views/tactics/new-market-tactics/market-tactics-page.vue'),
      meta: {
        title: '新增编辑营销策略',
        requiresAuth: true,
        bindButton: [
          marketTacticsAuthKey.TACTICS_MARKET_TACTICS_ADD,
          marketTacticsAuthKey.TACTICS_MARKET_TACTICS_MODIFY,
        ],
        roles: ['admin'],
        hideInMenu: true,
      },
    },
    {
      path: '/tactics/link-tactics',
      name: 'tacticsLinkTactics',
      component: () => import('@/views/tactics/link-tactics/index.vue'),
      meta: {
        title: '链接策略',
        requiresAuth: true,
        roles: ['admin'],
        icon: 'icon-link',
      },
    },
    {
      path: '/tactics/tacticsStore',
      name: 'tacticsStore',
      meta: {
        title: '策略库',
        requiresAuth: true,
        roles: ['admin'],
        icon: 'icon-folder',
      },
      children: [
        // {
        //   path: '/tactics/ad-tactics',
        //   name: 'tacticsAdTactics',
        //   component: () => import('@/views/tactics/ad-tactics/index.vue'),
        //   meta: {
        //     title: '广告策略库-v8',
        //     requiresAuth: true,
        //     roles: ['admin'],
        //   },
        // },
        {
          path: '/tactics/new-ad-tactics',
          name: 'tacticsNewAdTactics',
          component: () => import('@/views/tactics/new-ad-tactics/index.vue'),
          meta: {
            title: '广告策略库-v9',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/tactics/ad-tactics-v10',
          name: 'tacticsAdTacticsV10',
          component: () => import('@/views/tactics/ad-tactics-v10/index.vue'),
          meta: {
            title: '广告策略库-v10',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/tactics/ad-tactics-v11',
          name: 'tacticsAdTacticsV11',
          component: () => import('@/views/tactics/ad-tactics-v11/index.vue'),
          meta: {
            title: '广告策略库-v11',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/tactics/ad-tactics-v300',
          name: 'tacticsAdTacticsV300',
          component: () => import('@/views/tactics/ad-tactics-v300/index.vue'),
          meta: {
            title: '广告策略库-v300',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/tactics/ad-tactics-v12',
          name: 'tacticsAdTacticsV12',
          component: () => import('@/views/tactics/ad-tactics-v12/index.vue'),
          meta: {
            title: '广告策略库-v12',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/tactics/ad-tactics-v301',
          name: 'tacticsAdTacticsV301',
          component: () => import('@/views/tactics/ad-tactics-v301/index.vue'),
          meta: {
            title: '广告策略库-v301',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
      ],
    },
    {
      path: '/tactics/behavior-tactics',
      name: 'tacticsBehaviorTactics',
      component: () => import('@/views/tactics/behavior-tactics/index.vue'),
      meta: {
        title: '行为策略库',
        requiresAuth: true,
        roles: ['admin'],
        icon: 'icon-bold',
      },
    },
    {
      path: '/landing',
      name: 'landingPage',
      meta: {
        title: '落地页',
        requiresAuth: true,
        roles: ['admin'],
        icon: LandingPage as any,
      },
      children: [
        {
          path: '/landing/landingPageTactics',
          name: 'landingPageTactics',
          component: () =>
            import('@/views/tactics/landing-page-tactics/index.vue'),
          meta: {
            title: '落地页策略',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/landing/pullUpTactics',
          name: 'pullUpTactics',
          component: () => import('@/views/tactics/pull-up-tactics/index.vue'),
          meta: {
            title: '拉起策略库',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/landing/technologyTactics',
          name: 'technologyTactics',
          component: () =>
            import('@/views/tactics/technology-tactics/index.vue'),
          meta: {
            title: '技术策略库',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/landing/landingPkgTactics',
          name: 'landingPkgTactics',
          component: () => import('@/views/tactics/landing-tactics/index.vue'),
          meta: {
            title: '拉起包编辑',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
      ],
    },
    {
      path: '/tactics/game',
      name: 'game',
      meta: {
        title: '游戏',
        requiresAuth: true,
        roles: ['admin'],
        icon: 'icon-folder',
      },
      children: [
        {
          path: '/tactics/game/market-tactics',
          name: 'gameMarketTactics',
          component: () =>
            import('@/views/tactics/game/market-tactics/index.vue'),
          meta: {
            title: '营销策略',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/tactics/game/market-tactics/config',
          name: 'gameMarketTacticsConfig',
          component: () =>
            import(
              '@/views/tactics/game/market-tactics/market-tactics-page.vue'
            ),
          meta: {
            title: '新增编辑营销策略',
            requiresAuth: true,
            bindButton: [
              gameMarketTacticsAuthKey.GAME_MARKET_TACTICS_ADD,
              gameMarketTacticsAuthKey.GAME_MARKET_TACTICS_COPY,
              gameMarketTacticsAuthKey.GAME_MARKET_TACTICS_MODIFY,
            ],
            roles: ['admin'],
            hideInMenu: true,
          },
        },
        {
          path: '/tactics/game/sceneTactics',
          name: 'gameSceneTactics',
          component: () =>
            import('@/views/tactics/game/scene-tactics/index.vue'),
          meta: {
            title: '场景策略',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
        {
          path: '/tactics/game/adScene',
          name: 'gameAdScene',
          component: () => import('@/views/tactics/game/ad-scene/index.vue'),
          meta: {
            title: '广告场景',
            requiresAuth: true,
            roles: ['admin'],
          },
        },
      ],
    },
  ],
};

export default APPLICATION;
