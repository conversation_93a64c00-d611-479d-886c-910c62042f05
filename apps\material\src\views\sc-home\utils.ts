import { deriveField, deriveOptions } from '@/constants/material';

export const homeContext = 'scHomeContext';

export const NameType = {
  Vedio: 1,
  Image: 2,
  AiImage: 3,
};
export const batchTypeList = [
  { value: 1, label: '导入合成/解压小说' },
  { value: 2, label: '导入合成/滚屏小说' },
  { value: 6, label: '导入合成/融帧视频' },
];

export const nameTypeList = [
  { value: 1, label: '视频批量合成' },
  { value: 2, label: '图片批量合成' },
  { value: 3, label: 'AI批量生图' },
  { value: 4, label: '导入合成' },
];
export const statusTypeEnum = {
  draft: 1,
  wait: 1,
  processing: 2,
  completed: 3,
  terminated: 4,
  pushing: 5,
  pushDone: 6,
};
export const downloadStatusList = [
  { value: statusTypeEnum.wait, label: '待执行', type: 'info' },
  { value: statusTypeEnum.processing, label: '执行中', type: 'info' },
  { value: statusTypeEnum.completed, label: '已完成', type: 'success' },
  { value: statusTypeEnum.terminated, label: '失败', type: 'error' },
];

export const statusTypeList = [
  { value: statusTypeEnum.wait, label: '排队中', type: 'info' },
  { value: statusTypeEnum.processing, label: '合成中', type: 'info' },
  { value: statusTypeEnum.completed, label: '已完成', type: 'success' },
  { value: statusTypeEnum.terminated, label: '已终止', type: 'error' },
  { value: statusTypeEnum.pushing, label: '推送中', type: 'info' },
  { value: statusTypeEnum.pushDone, label: '已推送', type: 'success' },
];

export const batchPushStatusList = [
  { value: 1, label: '未推送', type: 'info' },
  { value: 2, label: '推送中', type: 'info' },
  { value: 3, label: '推送成功', type: 'success' },
  { value: 4, label: '推送失败', type: 'error' },
];
export const detailStatusEnum = {
  wait: 1,
  processing: 2,
  success: 3,
  fail: 4,
  initializing: 5,
  submitted: 5,
  lining: 6,
  performing: 7,
};
export const detailStatusList = [
  { value: detailStatusEnum.wait, label: '排队中', type: 'info' },
  { value: detailStatusEnum.processing, label: '合成中', type: 'info' },
  { value: detailStatusEnum.success, label: '已完成', type: 'success' },
  { value: detailStatusEnum.fail, label: '失败', type: 'error' },
  { value: detailStatusEnum.submitted, label: '已提审', type: 'success' },
];

export const batchDetailStatusList = [
  { value: 1, label: '排队中', type: 'info' },
  { value: 2, label: '合成中', type: 'info' },
  { value: 3, label: '合成完成', type: 'success' },
  { value: 4, label: '合成失败', type: 'error' },
  { value: 6, label: '已中止', type: 'info' },
  { value: 102, label: '推送中', type: 'info' },
  { value: 103, label: '推送完成', type: 'success' },
  { value: 104, label: '推送失败', type: 'error' },
];

export const pushStatusList = [
  { value: detailStatusEnum.wait, label: '推送中', type: 'info' },
  { value: detailStatusEnum.processing, label: '推送中', type: 'info' },
  { value: detailStatusEnum.success, label: '成功', type: 'success' },
  { value: detailStatusEnum.fail, label: '失败', type: 'error' },
  { value: detailStatusEnum.initializing, label: '初始化', type: 'info' },
  { value: detailStatusEnum.lining, label: '排队中', type: 'info' },
  { value: detailStatusEnum.performing, label: '执行中', type: 'info' },
];
export const uploadStatusList = [
  {
    value: 1,
    label: '成功',
    type: 'success',
  },
  { value: 2, label: '失败', type: 'error' },
  { value: 3, label: '正在处理', type: 'info' },
];
export const homeColumns = [
  {
    title: '批次ID',
    dataIndex: 'batchId',
    width: 80,
    fixed: 'left',
  },
  {
    title: '工具名称',
    dataIndex: 'toolName',
    slotName: 'toolName',
    width: 150,
  },
  {
    title: '任务状态',
    dataIndex: 'status',
    slotName: 'status',
    width: 87,
  },
  {
    title: '提交人',
    dataIndex: 'checkName',
    ellipsis: true,
    tooltip: true,
    width: 100,
  },
  {
    title: '更新时间',
    dataIndex: 'synTime',
    width: 170,
  },
  {
    title: '任务明细',
    dataIndex: 'taskStatus',
    slotName: 'taskStatus',
    width: 360,
  },
  {
    title: '操作',
    dataIndex: 'operations',
    slotName: 'operations',
    width: 220,
    fixed: 'right',
  },
];
export const batchColumns = [
  {
    title: '批次ID',
    dataIndex: 'id',
    width: 80,
    fixed: 'left',
  },
  {
    title: '工具名称',
    dataIndex: 'toolName',
    slotName: 'toolName',
    width: 150,
  },
  {
    title: '批次名称',
    dataIndex: 'taskName',
    ellipsis: true,
    tooltip: true,
  },
  {
    title: '任务状态',
    dataIndex: 'status',
    slotName: 'status',
    width: 87,
  },
  {
    title: '提交人',
    dataIndex: 'createdName',
    ellipsis: true,
    tooltip: true,
    width: 100,
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    width: 170,
  },
  {
    title: '更新时间',
    dataIndex: 'synTime',
    slotName: 'synTime',
    width: 170,
  },
  {
    title: '任务明细',
    dataIndex: 'taskStatus',
    slotName: 'taskStatus',
    width: 200,
  },
  {
    title: '操作',
    dataIndex: 'operations',
    slotName: 'operations',
    width: 200,
    fixed: 'right',
  },
];
export const pushColumns = [
  {
    title: '批次ID',
    dataIndex: 'batchId',
    width: 170,
  },
  {
    title: '推送平台',
    dataIndex: 'pushPlatform',
    slotName: 'pushPlatform',
    width: 100,
  },
  {
    title: '推送专辑',
    dataIndex: 'commitAlbumName',
    ellipsis: true,
    slotName: 'commitAlbumName',
  },
  {
    title: '提交人',
    dataIndex: 'createdUname',
  },
  {
    title: '任务状态',
    dataIndex: 'taskStatus',
    slotName: 'taskStatus',
    width: 300,
  },
  {
    title: '操作',
    dataIndex: 'operations',
    slotName: 'operations',
    width: 150,
  },
];
export const uploadColumns = [
  {
    title: '批次ID',
    dataIndex: 'batchId',
    width: 170,
    fixed: 'left',
  },
  {
    title: '推送平台',
    dataIndex: 'pushPlatform',
    slotName: 'pushPlatform',
    width: 100,
  },
  {
    title: '推送专辑',
    dataIndex: 'commitAlbumName',
    ellipsis: true,
    slotName: 'commitAlbumName',
    width: 100,
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    width: 170,
  },
  {
    title: '任务状态',
    dataIndex: 'taskStatus',
    slotName: 'taskStatus',
    width: 350,
  },
  {
    title: '操作',
    dataIndex: 'operations',
    slotName: 'operations',
    width: 150,
    fixed: 'right',
  },
];
export function getFileExtension(filename) {
  if (!filename) {
    return '';
  }
  // 找到最后一个 '.' 的索引
  const lastDotIndex = filename.lastIndexOf('.');

  // 如果没有找到 '.' 或者 '.' 是第一个字符，返回空字符串
  if (lastDotIndex === -1 || lastDotIndex === 0) {
    return '';
  }

  // 返回从最后一个 '.' 之后的子字符串
  return filename.substring(lastDotIndex + 1);
}

export function getDeriveSchema() {
  return {
    name: deriveField,
    label: '衍生处理',
    type: 'select',
    format: 'radio',
    required: true,
    hideAsterisk: true,
    source: {
      data: deriveOptions,
    },
    labelTip:
      '当前素材如果之前上传过创量，现在需要重复使用，可以选择“复制入库”，如果不需要就选择“不入库”推送的时候会拦截掉重复素材',
    // visibleOn: (_value, form) => form?.pushPlatform === 2,
  };
}
export default null;
