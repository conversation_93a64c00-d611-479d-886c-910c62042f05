import { DPage } from '@repo/kola/src/components/dynamic/types/page';
import { DButton } from '@repo/kola/src/components/dynamic/types/button';
import { Message } from '@arco-design/web-vue';
import useUserConfigStore from '@/store/modules/config';
import { configApi } from '@/api/setting/config';
import { formSchema, currentId } from './formSchema';

const titleText = '新建规则';
const formatValidSize = async (validSize: any[]) => {
  const sizeArray = await useUserConfigStore().getPixelList();
  const result = {};
  validSize.forEach((item: any) => {
    const target = sizeArray.find((val: any) => val.sizeText === item);
    result[target.sizeText] = target.size;
  });
  return result;
};
/** Modal Button */
export const getModalButtonConfig = (callback?: () => void): DButton => ({
  text: titleText,
  auth: 'setting_config_add',
  // 如果非动态button 请 用 v-auth='ksPackageAuth.PROPERTY_KS_PACKAGE_CREATE'
  props: {
    type: 'primary',
    loading: false,
  },
  clickActionType: 'drawer',
  linkUrl: '',
  modal: {
    props: {
      title: titleText,
      width: '750px',
    },
    contentType: 'form',
    form: {
      formSchema,
    },
    getDefaultValue: () => {
      currentId.value = undefined;
      return {
        name: '本地上传',
        sizeLimit: {
          imageMaxUnit: 'KB',
          imageMaxSymbol: '>=',
          imageMinSymbol: '>=',
          imageMinUnit: 'KB',
          videoMaxUnit: 'MB',
          videoMaxSymbol: '>=',
          videoMinSymbol: '>=',
          videoMinUnit: 'MB',
        },
        durationLimit: {
          videoDurationMaxUnit: 'S',
          videoDurationMaxSymbol: '>=',
          videoDurationMinSymbol: '>=',
          videoDurationMinUnit: 'S',
        },
        validUserIds: [],
      };
    },

    action: async (value: any): Promise<boolean | void> => {
      const { refreshTable, formData } = value;
      const { name, validUserIds, validSize, sizeLimit, durationLimit } =
        formData;
      if (
        validSize.length === 0 &&
        !sizeLimit.imageMinFileSize &&
        !sizeLimit.imageMaxFileSize &&
        !sizeLimit.videoMinFileSize &&
        !sizeLimit.videoMaxFileSize &&
        !durationLimit.videoDurationMax &&
        !durationLimit.videoDurationMin
      ) {
        Message.warning('请配置有效的拦截设置');
        return false;
      }

      const params = {
        name,
        validUserIds: validUserIds?.join(','),
        validSize: await formatValidSize(validSize),
        imageMinFileSize:
          sizeLimit.imageMinFileSize !== undefined
            ? String(sizeLimit.imageMinFileSize) || ''
            : '',
        imageMaxFileSize:
          sizeLimit.imageMaxFileSize !== undefined
            ? String(sizeLimit.imageMaxFileSize) || ''
            : '',
        videoMinFileSize:
          sizeLimit.videoMinFileSize !== undefined
            ? String(sizeLimit.videoMinFileSize) || ''
            : '',
        videoMaxFileSize:
          sizeLimit.videoMaxFileSize !== undefined
            ? String(sizeLimit.videoMaxFileSize) || ''
            : '',
        videoDurationMax:
          durationLimit.videoDurationMax !== undefined
            ? String(durationLimit.videoDurationMax) || ''
            : '',
        videoDurationMin:
          durationLimit.videoDurationMin !== undefined
            ? String(durationLimit.videoDurationMin) || ''
            : '',
        imageMaxUnit: sizeLimit.imageMaxUnit,
        imageMaxSymbol: sizeLimit.imageMaxSymbol,
        imageMinSymbol: sizeLimit.imageMinSymbol,
        imageMinUnit: sizeLimit.imageMinUnit,
        videoMaxUnit: sizeLimit.videoMaxUnit,
        videoMaxSymbol: sizeLimit.videoMaxSymbol,
        videoMinSymbol: sizeLimit.videoMinSymbol,
        videoMinUnit: sizeLimit.videoMinUnit,
        videoDurationMaxUnit: durationLimit.videoDurationMaxUnit,
        videoDurationMaxSymbol: durationLimit.videoDurationMaxSymbol,
        videoDurationMinSymbol: durationLimit.videoDurationMinSymbol,
        videoDurationMinUnit: durationLimit.videoDurationMinUnit,
      };
      await configApi.configAdd?.(params);
      Message.success('新增成功');
      if (callback) {
        callback?.();
      }
      return refreshTable();
    },
  },
});
const operation: DPage['operation'] = [getModalButtonConfig()];

export default operation;
