<template>
  <a-spin class="app-package" :loading="fetchPkgLoading">
    <a-input :disabled="true" :model-value="model.packageName"></a-input>
    <DynamicButton :config="config"></DynamicButton>
  </a-spin>
</template>

<script setup lang="ts">
  import { getAppListApi, getQuickAppInfo } from '@/api/ad-batch/toutiao/app';
  import { useAutomatedTtCommonData } from '@/views/automated-setup/toutiao/config/hooks/use-automated-tt-data';
  import { useAppStore } from '@/store';
  import { find } from 'lodash';
  import { computed } from 'vue';
  import { Message, useFormItem } from '@arco-design/web-vue';
  import { batchTtAssetPrepareApi } from '@/api/ad-batch/toutiao/schedule-arm';

  import { automatedSetupToutiaoApi } from '@/api/automated-setup/toutiao';

  const model = defineModel<{
    packageName: string;
    appName: string;
  }>();

  const {
    accountList,
    isSetDefault,
    assetType,
    setQuickAppInfo,
    fetchPkgLoading,
    setFetchPkgLoading,
  } = useAutomatedTtCommonData();

  async function fetchPkg() {
    if (accountList.value?.length <= 0 || !model.value?.packageName) {
      return;
    }
    try {
      setFetchPkgLoading(true);
      const quickAppInfoRes = await getQuickAppInfo({
        advertiserIds: accountList.value.map(({ accountId }) => accountId),
        packageName: model.value?.packageName || '',
      });
      const list = quickAppInfoRes.data ?? [];
      if (
        accountList.value.some(
          ({ accountId }) =>
            !list.find((item) => item.advertiserId === accountId)
        )
      ) {
        resetPkg();
        Message.warning('已选账户下未检测到的快应用');
      } else {
        if (!model.value?.packageName) {
          if (model.value && !isSetDefault.value) {
            model.value.appName = list?.[0]?.name ?? '';
            model.value.packageName = list?.[0]?.packageName ?? '';
            eventHandlers.value.onChange?.();
          }
        }
        setQuickAppInfo(list);
      }
    } catch (e) {
      console.log('e', e);
      resetPkg();
    } finally {
      setFetchPkgLoading(false);
      eventHandlers.value.onChange?.();
    }
  }
  const { eventHandlers } = useFormItem();

  function resetPkg() {
    setQuickAppInfo([]);
    model.value = {
      packageName: '',
      appName: '',
    };
  }
  const config = {
    text: '选择已有',
    props: {
      type: 'primary',
      loading: false,
      disabled: () => {
        return accountList.value?.length <= 0;
        // return !quickAppInfo.value.length;
      },
    },
    clickActionType: 'modal',
    modal: {
      props: {
        title: '选择应用',
        width: 520,
      },
      contentType: 'form',
      form: {
        formSchema: {
          fields: [
            {
              label: '选择应用',
              name: 'appData',
              placeholder: '请分别在所选账户下创建相同的快应用信息',
              type: 'select',
              format: 'singleSelect',
              required: true,
              select: {
                allowSearch: true,
                allowClear: true,
                valueKey: 'packageName',
                tail: {
                  type: 'DButton',
                  position: 'inner',
                  props: (getSourceData) => ({
                    props: {
                      type: 'text',
                    },
                    icon: 'icon-sync',
                    clickActionType: 'action',
                    action: async () => {
                      const query: any = {
                        advertiserIds: accountList.value.map(
                          (item) => item.accountId
                        ),
                        platform: 101,
                        assetType: 'QUICK_APP',
                      };
                      await automatedSetupToutiaoApi.syncAssetList(query);
                      getSourceData?.();
                    },
                  }),
                },
              },
              source: {
                data: () => {
                  return getAppListApi({
                    advertiserIds: accountList.value.map(
                      (account: any) => account.accountId
                    ),
                  }).then((res) => {
                    const list = res?.data ?? [];
                    if (list.length <= 0) {
                      Message.warning('当前账户下未检测到的快应用');
                    }
                    return list.map((item) => {
                      return {
                        label: item.name,
                        value: {
                          appName: item.name,
                          packageName: item.packageName,
                        },
                      };
                    });
                  });
                },
              },
            },
          ],
        },
      },
      getDefaultValue: () => {
        // 后端用于预先创建资产信息
        batchTtAssetPrepareApi({
          advertiserIds: accountList.value?.map((item) => item.accountId),
          assetType: assetType.value,
        });
        return {
          appData: undefined,
        };
      },
      action: async (value: any) => {
        const { formData } = value;

        if (model.value) {
          model.value.appName = formData.appData?.appName;
          model.value.packageName = formData.appData?.packageName;
          fetchPkg();
        }
      },
    },
  };
</script>

<style scoped lang="less">
  .app-package {
    display: flex;
    gap: 16px;
  }
</style>
