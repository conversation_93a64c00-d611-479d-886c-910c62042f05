import { http } from '@/utils/http';

const abandonPkgManage = {
  list(data) {
    return http.post('/v3/setting/abandon-pkg/list', data);
  },
  create(data) {
    return http.post('/v3/setting/abandon-pkg/add', data);
  },
  remove(data) {
    return http.post('/v3/setting/abandon-pkg/delete', data);
  },
  batchUpload(data) {
    return http.post('/v3/setting/abandon-pkg/batch-add', data);
  },
  modify(data) {
    return http.post('/v3/setting/abandon-pkg/edit', data);
  },
  detail(data) {
    return http.post('/v3/setting/abandon-pkg/detail', data);
  },
  adSearchList(data) {
    return http.post('/advancement/abandon-pkg/app-store-quick-app-list', data);
  },
};

export default abandonPkgManage;
