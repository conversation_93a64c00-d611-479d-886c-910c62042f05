import abandonPkgManage from '@/api/advanced/abandon-pkg';

function getTable() {
  return {
    hideOperations: true,
    load: {
      action: async (filter, pagination) => {
        return abandonPkgManage.adSearchList?.({
          ...pagination,
        });
      },
    },
    columns: [
      {
        title: '应用名称',
        dataIndex: 'name',
      },
      {
        title: '包名',
        dataIndex: 'pkg',
      },
      {
        title: '查询到的时间',
        dataIndex: 'createdAt',
      },
      {
        title: '是否已添加',
        dataIndex: 'syncStatus',
        customRender: {
          type: 'code-to-name',
          props: {
            map: [
              {
                label: '未同步',
                value: 1,
              },
              {
                label: '已同步',
                value: 2,
              },
            ],
          },
        },
      },
    ],
  };
}

export default getTable;
