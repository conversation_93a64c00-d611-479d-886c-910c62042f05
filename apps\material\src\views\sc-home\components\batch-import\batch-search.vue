<template>
  <div class="home-search">
    <DynamicForm
      class="dynamic-form"
      v-model="formData"
      :form-schema="formSchema"
      layout="inline"
    >
    </DynamicForm>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import dayjs from 'dayjs';
  import { cloneDeep } from 'lodash';

  function getCurrentDay(day) {
    return dayjs().subtract(day, 'day').format('YYYY-MM-DD');
  }

  const getDefaultFormData = () => {
    return {
      dateRange: [],
      ids: '',
    };
  };
  const formData = ref(getDefaultFormData());
  const emits = defineEmits(['search']);

  function emitValue() {
    emits('search', getValue());
  }

  function getValue() {
    const value: any = cloneDeep(formData.value);
    value.startDay = value.dateRange?.[0];
    value.endDay = value.dateRange?.[1];
    value.ids = !value.ids
      ? []
      : value.ids?.split(',').map((item) => Number(item));
    delete value.dateRange;
    return value;
  }

  let oldSearchValue = '';
  const formSchema = {
    fields: [
      {
        label: '搜索',
        name: 'ids',
        type: 'text',
        style: {},
        placeholder: '请输入批次ID',
        allowClear: true,
        onBlur: ({ value }) => {
          if (oldSearchValue !== value) {
            oldSearchValue = value;
            emitValue();
          }
        },
      },
      {
        label: '创建时间',
        name: 'dateRange',
        type: 'dateRange',
        // disabledDate: (current) => {
        //   // 最多支持往前30天
        //   const fortyFiveDay = dayjs().subtract(31, 'day');
        //   const nextDay = dayjs().add(0, 'day');
        //   return (
        //     dayjs(current).isBefore(fortyFiveDay) ||
        //     dayjs(current).isAfter(nextDay)
        //   );
        // },
        onChange: () => {
          setTimeout(() => {
            emitValue();
          }, 0);
        },
      },
    ],
  };

  defineExpose({
    getValue,
  });
</script>

<style scoped lang="less"></style>
