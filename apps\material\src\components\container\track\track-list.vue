<template>
  <div class="track-list" ref="trackListContainer">
    <template
      v-for="(lineData, lineIndex) of showTrackList"
      :key="lineData.list.reduce((r, item) => r + item.id, 'line')"
    >
      <TrackLine
        :line-type="lineData.type"
        :is-active="store.selectTrackItem.line === lineIndex"
        :line-index="lineIndex"
        :line-data="lineData.list"
      />
    </template>
  </div>
</template>

<script setup lang="ts">
  import type { VideoSource } from '@/class/videoTrack';
  import { useTrackState } from '@/store/modules/track-state';
  import { formatTime } from '@/utils/track-utils';
  import { computed, ref } from 'vue';
  import TrackLine from './track-line.vue';

  const store = useTrackState();
  const mainIndex = ref(0); // main 行下标

  const showTrackList = computed(() => {
    return store.trackList.map((line, lineIndex) => {
      line.main && (mainIndex.value = lineIndex);
      const newList = line.list.map((item) => {
        const { duration: time } = item.source as VideoSource;
        return {
          ...item,
          //   showWidth: `${getGridPixel(
          //     trackScale.value,
          //     item.end - item.start
          //   )}px`,
          //   showLeft: `${getGridPixel(trackScale.valu, item.start)}px`,
          showWidth: 200,
          showLeft: 0,
          time: line.type === 'video' ? `${formatTime(time || 0).str}` : '',
        };
      });
      return {
        ...line,
        list: newList,
      };
    });
  });
</script>

<style scoped lang="less">
  .track-list {
    position: absolute;
    top: 28px;
    height: calc(100% - 20px);
    width: 100%;
    overflow: auto;
    padding-bottom: 20px;
  }
</style>
