export const reportSourcePkgContext = 'REPORT_SOURCE_PKG_CONTEXT';
export const sourcePkgLocalKey = 'report_source_pkg_fixed_column';
export const reportSourcePkgMediaOptions = [
  { label: '今日头条', value: 'toutiao2' },
  { label: '快手', value: 'kuaishou' },
  { label: '广点通', value: 'gdt3' },
  { label: '百度', value: 'baidu' },
  { label: 'VIVO', value: 'vivo' },
  { label: 'OPPO', value: 'oppo' },
  { label: '小米', value: 'xiaomi' },
  { label: 'UC', value: 'uc' },
  { label: '华为', value: 'huawei' },
  { label: '趣头条', value: 'qtt' },
  { label: '支付宝', value: 'alipay' },
  { label: '微博', value: 'weibo' },
  { label: 'WIFI万能钥匙', value: 'wifi' },
  { label: '荣耀', value: 'honor' },
  { label: '爱奇艺', value: 'iqiyi' },
];

export const reportSourcePkgDimensionEnum = {
  account: 'account_id',
};
export const reportSourcePkgDimensionOptions = [
  { label: '账户/链接', value: reportSourcePkgDimensionEnum.account },
];
export const sourcePkgReportType = {
  reportType: 10,
};
export const getAllFixedConstantsColumns = () => {
  return [
    {
      label: '日期',
      value: 'date',
    },
    {
      label: '广告位ID',
      value: 'ad_id',
    },
    {
      label: '广告位名称',
      value: 'ad_name',
    },
    {
      label: '广告位类型',
      value: 'ad_type',
    },

    {
      label: '厂商编码',
      value: 'pop_brand_code',
    },
    {
      label: '厂商名称',
      value: 'pop_brand_name',
    },
    {
      label: '媒体编码',
      value: 'media_code',
    },
    {
      label: '媒体名称',
      value: 'media_name',
    },
    {
      label: '账号ID',
      value: 'account_id',
    },
    {
      label: '账号名称',
      value: 'account_name',
    },
    {
      label: '链接ID',
      value: 'link_id',
    },
    {
      label: '计划ID',
      value: 'campaign_id',
    },
    {
      label: '计划名称',
      value: 'campaign_name',
    },
    {
      label: '创意ID',
      value: 'creative_id',
    },
    {
      label: '创意名称',
      value: 'creative_name',
    },
    {
      label: '优化师',
      value: 'optimizer_id',
    },
    {
      label: '优化师名称',
      value: 'optimizer_name',
    },
    {
      label: '产品ID',
      value: 'app_id',
    },
    {
      label: '产品名称',
      value: 'app_name',
    },
    {
      label: '渠道Code',
      value: 'channel_code',
    },
    {
      label: '渠道名称',
      value: 'channel_name',
    },
    {
      label: '媒体更新时间',
      value: 'media_update_time',
    },
    {
      label: '广告位更新时间',
      value: 'ad_update_time',
    },
    {
      label: '投放范围编码',
      value: 'channel_type',
    },
    {
      label: '投放范围名称',
      value: 'channel_type_name',
    },
    {
      label: '投放范围',
      value: 'channel_type_name',
    },
    {
      label: '来源包',
      value: 'source_pkg',
    },
  ];
};
export const getFixedFieldColumnsMap = () => {
  return {
    [reportSourcePkgDimensionEnum.account]: [
      {
        dataIndex: 'date',
        isShow: true,
      },
      {
        dataIndex: 'source_pkg',
        isShow: true,
      },
      {
        dataIndex: 'pop_brand_name',
        isShow: true,
      },
    ],
  };
};
export const getRelevanceMap = () => {
  return {
    [reportSourcePkgDimensionEnum.account]: [
      {
        label: '产品',
        value: {
          fields: ['app_name'],
          isShow: true,
        },
      },
      {
        label: '渠道',
        value: {
          fields: ['channel_code', 'channel_name'],
          isShow: true,
        },
      },
      {
        label: '账户/链接',
        value: {
          isShow: true,
          fields: ['link_id', 'account_name', 'account_id'],
        },
      },
    ],
  };
};

export const getReportSourcePkgEndColumns = () => {
  return [
    {
      title: '投放数据更新时间',
      dataIndex: 'media_update_time',
      width: 162,
    },
    {
      title: '优数更新时间',
      dataIndex: 'bury_update_time',
      width: 162,
    },
  ];
};
