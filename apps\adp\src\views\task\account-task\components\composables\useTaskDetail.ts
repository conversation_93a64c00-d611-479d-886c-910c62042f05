import { reactive, ref } from 'vue';
import { operationTask } from '@/api/task/operation-task';
import { mediaPlatformMap } from '@/views/promotion/hooks/authorization';
import { Message, Modal, PaginationProps } from '@arco-design/web-vue';

/**
 * 任务详情数据管理 Composable
 * @param record 任务记录
 * @param currentPage 当前页面信息
 */
export function useTaskDetail(record: any, currentPage: any) {
  const currentKey = ref<string | number>('ACCOUNT');
  const baseInfo = ref<any>([]);
  const filterForm = ref<any>({
    section: [],
  });
  const tableDataList = ref<any>({
    ACCOUNT: [],
    CAMPAIGN: [],
    UNIT: [],
    SITE: [],
    CREATIVE: [],
    FAIL_REASON: [],
  });
  const operationTargetList = ref<any>([]);
  const loading = ref(false);

  // 分页参数管理
  const paginationParams = reactive<PaginationProps>({
    current: 1,
    pageSize: 100,
    total: 0,
    showPageSize: true,
    showJumper: true,
    showTotal: true,
    pageSizeOptions: [10, 20, 30, 40, 50, 100, 200, 300, 400, 500],
  });

  /**
   * 处理任务详情数据
   * @param item 任务详情项
   */
  function processTaskDetailItem(item: any) {
    item.operationTargetValue = item.operationTargetValue
      ? JSON.parse(item.operationTargetValue)
      : [];
    item.originValue = item.originValue ? JSON.parse(item.originValue) : [];

    if (
      Array.isArray(item.operationTargetValue) &&
      Array.isArray(item.originValue)
    ) {
      item.showList = item.originValue.map((originItem, orIndex) => {
        return {
          ...originItem,
          oldPropertyName: originItem.propertyName,
          oldTargetName: originItem.targetName,
          newPropertyName: item.operationTargetValue[orIndex]?.propertyName,
          newTargetName: item.operationTargetValue[orIndex]?.targetName,
        };
      });
    } else {
      item.showList = [];
    }
  }

  /**
   * 获取任务详情数据
   */
  async function fetchDetail() {
    if (!record?.id) return;

    loading.value = true;
    try {
      if (currentKey.value === 'ACCOUNT') {
        const res = await operationTask.detail({
          taskId: record.id,
          ...filterForm.value,
        });

        baseInfo.value = res.data;
        // 处理任务详情列表数据
        res.data.taskDetailRespList.forEach(processTaskDetailItem);
        tableDataList.value[currentKey.value] = res.data.taskDetailRespList;
      } else if (currentKey.value === 'FAIL_REASON') {
        const res = await operationTask.failDetail({
          operationTaskId: record.id,
          ...filterForm.value,
        });
        // todo
        tableDataList.value[currentKey.value] = res.data;
      } else {
        const res = await operationTask.detailTabsList({
          platform: currentPage.mediaType,
          operationTaskId: record.id,
          type: currentKey.value,
          ...filterForm.value,
          pageNum: paginationParams.current,
          pageSize: paginationParams.pageSize,
        });

        tableDataList.value[currentKey.value] = res.data.list;
        paginationParams.current = res.data.current;
        paginationParams.pageSize = res.data.size;
        paginationParams.total = res.data.total;
      }
    } catch (error) {
      // console.error('获取任务详情失败:', error);
      // Message.error('获取任务详情失败');
    } finally {
      loading.value = false;
    }
  }

  /**
   * 获取操作目标类型列表
   */
  async function getOperationList() {
    if (!currentPage?.mediaType) return;

    try {
      const res = await operationTask.getOperationTargetType({
        mediaPlatform: mediaPlatformMap[currentPage.mediaType],
        taskSource: 1,
      });
      operationTargetList.value = res.data;
    } catch (error) {
      // console.error('获取操作列表失败:', error);
    }
  }

  /**
   * 取消任务操作
   * @param row 要取消的任务行数据
   */
  function handleCancel(row: any) {
    Modal.open({
      title: '取消',
      content: `确定后不执行本次任务？`,
      onBeforeOk: (done) => {
        operationTask
          .detailStop({ id: row.id })
          .then(() => {
            fetchDetail(); // 重新获取数据
            done(true);
            Message.success('操作成功');
          })
          .catch((err) => {
            // console.error('取消任务失败:', err);
            Message.error('取消任务失败');
            done(false);
          });
      },
    });
  }

  /**
   * 处理分页变化
   */
  function handlePageChange(page: number) {
    paginationParams.current = page;
    fetchDetail();
  }

  /**
   * 处理分页大小变化
   */
  function handlePageSizeChange(pageSize: number) {
    paginationParams.current = 1;
    paginationParams.pageSize = pageSize;
    fetchDetail();
  }

  /**
   * 切换tab时重置分页
   */
  function handleTabChange(key: string | number) {
    // 切换tab时重置为第一页
    paginationParams.current = 1;
    filterForm.value.section = [];
    fetchDetail();
  }

  /**
   * 初始化数据
   */
  function initData() {
    fetchDetail();
    getOperationList();
  }

  return {
    // 响应式数据
    currentKey,
    baseInfo,
    filterForm,
    tableDataList,
    operationTargetList,
    loading,
    paginationParams,

    // 方法
    fetchDetail,
    getOperationList,
    handleCancel,
    handlePageChange,
    handlePageSizeChange,
    handleTabChange,
    initData,
  };
}
