<template>
  <div class="container">
    <DynamicPage
      ref="dynamicPageRef"
      :operation="operation"
      :table="table"
      :filter="filter"
      title="曝光后遗弃包"
    ></DynamicPage>
  </div>
</template>

<script setup lang="ts">
  import authKey from '@/constants/auth-key/advanced/abandon-manage';
  import abandonPkgManage from '@/api/advanced/abandon-pkg';
  import getFilter from '../common/filter';
  import getOperation, {
    getFormSchema,
    getFormData,
  } from '../common/operation';
  import getTable from '../common/table';

  const filter = getFilter();

  const table = getTable(2);

  const operation = {
    ...getOperation(
      2,
      authKey.EXPOSURE_ABANDON_ADD,
      authKey.EXPOSURE_ABANDON_DELETE
    ),
    modify: {
      auth: authKey.EXPOSURE_ABANDON_MODIFY,
      form: {
        formSchema: getFormSchema(2),
        defaultFormData: getFormData(),
      },
      ui: {
        width: 650,
      },
      fetchDetail: (data) => {
        return abandonPkgManage.detail?.({ ...data, type: 2 });
      },
      action: (data) => {
        return abandonPkgManage.modify?.({ ...data, type: 2 });
      },
    },
  };
</script>

<style scoped lang="less">
  .container {
    height: 100%;
  }
</style>
