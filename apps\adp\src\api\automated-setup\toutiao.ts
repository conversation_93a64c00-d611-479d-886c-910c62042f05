import { http } from '@/utils/http';

export const automatedSetupToutiaoApi = {
  list(data) {
    return http.post('/adplan/auto/create/strategy/v2/page', data);
  },
  create(data) {
    return http.post('/adplan/auto/create/strategy/v2/add', data);
  },
  modify(data) {
    return http.post('/adplan/auto/create/strategy/v2/update', data);
  },
  detail(data) {
    return http.get('/adplan/auto/create/strategy/v2/detail', data);
  },
  delete(data) {
    return http.get('/adplan/auto/create/strategy/v2/delete', data);
  },
  toggleStatus(data) {
    return http.get('/adplan/auto/create/strategy/v2/updateStatus', data);
  },
  execute(data) {
    return http.get('/adplan/auto/create/strategy/v1/execute', data);
  },
  getCount(data) {
    return http.post('/adplan/auto/unit/prepare/v2/ad/count', data);
  },
  recordList(data) {
    return http.post('/adplan/auto/create/record/v1/page', data);
  },
  recordAccountList(data) {
    return http.get('/adplan/auto/create/record/v1/account/list', data);
  },
  retry(data) {
    return http.post('/adplan/auto/create/record/v1/retry', data);
  },
  filterAppList(data) {
    return http.get('/adplan/auto/create/strategy/v2/app/list', data);
  },
  filterRecordAppList(data) {
    return http.get('/adplan/auto/create/record/v1/app/list', data);
  },
  constantDetail(data) {
    return http.get('/adplan/auto/base/v1/tt/promotion/query', data);
  },
  getMaterialCount(data) {
    return http.post('/adplan/auto/base/v1/material/count', data);
  },
  getTextCount(data) {
    return http.post('/adplan/auto/base/v1/titlePkg/count', data);
  },
  checkName(data) {
    return http.get('/adplan/auto/create/strategy/v2/checkName', data);
  },
  syncAssetList(data) {
    return http.post('/adplan/asset/v1/sync', data);
  },
};
