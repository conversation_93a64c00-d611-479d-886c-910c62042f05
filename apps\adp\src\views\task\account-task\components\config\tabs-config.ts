import { MediaType } from '@/views/promotion/hooks/authorization';
import { TaskType } from '../../constants';

export interface TabConfig {
  key: string;
  title: string;
}

const DEFAULT_TABS: TabConfig = { key: 'ACCOUNT', title: '账户信息' };
export const TOU_TIAO_TABS: TabConfig[] = [
  DEFAULT_TABS,
  { key: 'CAMPAIGN', title: '项目信息' },
  { key: 'UNIT', title: '广告信息' },
  { key: 'SITE', title: '落地页信息' },
  { key: 'FAIL_REASON', title: '失败原因分布' },
];
export const KUAI_SHOU_TABS: TabConfig[] = [
  DEFAULT_TABS,
  { key: 'UNIT', title: '广告信息' },
  { key: 'CREATIVE', title: '创意信息' },
  { key: 'FAIL_REASON', title: '失败原因分布' },
];

/**
 * 根据平台类型获取Tab配置
 * @param mediaType 媒体平台类型
 * @param record 任务记录
 * @returns Tab配置数组
 */
export function getTabsConfig(mediaType: MediaType, record: any): TabConfig[] {
  const tabConfigs = {
    // 账户换投
    [TaskType.REPLACE]: {
      [MediaType.TOU_TIAO]: TOU_TIAO_TABS,
      [MediaType.KUAI_SHOU]: KUAI_SHOU_TABS,
    },
  };

  const tabs = tabConfigs[record.taskType]?.[mediaType] || [DEFAULT_TABS];
  return tabs;
}
